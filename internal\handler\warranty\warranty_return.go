package warranty

import (
	"github.com/gin-gonic/gin"
	warranty "marketing/internal/service"
)

type WarrantyReturnHandler interface {
	Import(c *gin.Context)
	DoImport(c *gin.Context)

	Index(c *gin.Context)
	Store(c *gin.Context)
	Show(c *gin.Context)
	Update(c *gin.Context)
	Delete(c *gin.Context)
}

type warrantyReturnHandler struct {
	warrantyService warranty.InterfaceWarranty
}

func (w warrantyReturnHandler) Import(c *gin.Context) {
	//TODO implement me
	panic("implement me")
}

func (w warrantyReturnHandler) DoImport(c *gin.Context) {
	//TODO implement me
	panic("implement me")
}

func (w warrantyReturnHandler) Index(c *gin.Context) {
	//TODO implement me
	panic("implement me")
}

func (w warrantyReturnHandler) Store(c *gin.Context) {
	//TODO implement me
	panic("implement me")
}

func (w warrantyReturnHandler) Show(c *gin.Context) {
	//TODO implement me
	panic("implement me")
}

func (w warrantyReturnHandler) Update(c *gin.Context) {
	//TODO implement me
	panic("implement me")
}

func (w warrantyReturnHandler) Delete(c *gin.Context) {
	//TODO implement me
	panic("implement me")
}
