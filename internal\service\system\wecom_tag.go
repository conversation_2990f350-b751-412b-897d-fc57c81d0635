package system

import (
	"errors"
	"fmt"
	"github.com/gin-gonic/gin"
	"github.com/spf13/cast"
	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"marketing/internal/api"
	api2 "marketing/internal/api/system"
	"marketing/internal/config"
	"marketing/internal/model"
	appError "marketing/internal/pkg/errors"
	"marketing/internal/pkg/wecom"
	"strings"
	"time"
)

type WecomTagInterface interface {
	Add(c *gin.Context, req api2.AddWecomTagReq) error
	List(c *gin.Context, param api2.WecomTagReq) (*api.PaginationListResp, error)
	Update(c *gin.Context, req api2.AddWecomTagReq) error
	Delete(c *gin.Context, id uint) error
	ListNoPage(c *gin.Context, tagName string) ([]model.WecomTag, error)
	SyncTagsFormWecom(c *gin.Context) ([]model.WecomUserTag, error)
	MoveDepartment(c *gin.Context, test string) (string, error)
}

type wecomTagSvc struct {
	db          *gorm.DB
	wecomClient *wecom.Client
}

// NewWecomTagSvc creates a new WecomTagSvc instance
func NewWecomTagSvc(db *gorm.DB, cfg *config.Config) WecomTagInterface {
	wecomClient := wecom.NewWeComClient(cfg.WeCom.CorpID, cfg.WeCom.ContactsSecret)
	return &wecomTagSvc{
		db:          db,
		wecomClient: wecomClient,
	}
}

func (svc *wecomTagSvc) Add(c *gin.Context, req api2.AddWecomTagReq) error {
	var existTag model.WecomTag

	err := svc.db.WithContext(c).Where("tag_name = ?", req.TagName).First(&existTag).Error
	if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
		return err
	}
	if existTag.ID > 0 {
		return appError.NewErr("标签名：【" + req.TagName + "】已经存在")
	}

	//同步企微标签
	return svc.db.WithContext(c).Transaction(func(tx *gorm.DB) error {
		var tagData = &model.WecomTag{
			TagName: req.TagName,
		}
		if err := tx.Create(tagData).Error; err != nil {
			return err
		}

		// Call WeCom API to sync the tag
		if _, err := svc.wecomClient.CreateTag(req.TagName, tagData.ID); err != nil {
			return err
		}

		if err := tx.Save(tagData).Error; err != nil {
			return err
		}
		return nil
	})
}

func (svc *wecomTagSvc) List(c *gin.Context, param api2.WecomTagReq) (*api.PaginationListResp, error) {
	var r api.PaginationListResp
	var list []api2.WecomTagResp

	query := svc.db.WithContext(c).Model(&model.WecomTag{})

	if param.TagName != "" {
		query = query.Where("tag_name like ?", "%"+param.TagName+"%")
	}

	err := query.Count(&r.Total).Error
	if err != nil {
		return &r, err
	}

	limit, offset := param.PageSize, (param.Page-1)*param.PageSize
	err = query.Order("tag_name asc").Limit(limit).Offset(offset).Scan(&list).Error
	if err != nil {
		return &r, err
	}

	r.Data, r.PageSize, r.Page = list, limit, param.Page

	return &r, nil
}

func (svc *wecomTagSvc) Update(c *gin.Context, req api2.AddWecomTagReq) error {
	var existTag model.WecomTag

	err := svc.db.WithContext(c).Where("id = ?", req.ID).First(&existTag).Error
	if errors.Is(err, gorm.ErrRecordNotFound) {
		return appError.NewErr("标签不存在")
	} else if err != nil {
		return err
	}

	// Call WeCom API to sync the tag
	if err := svc.wecomClient.UpdateTag(int(existTag.ID), req.TagName); err != nil {
		return err
	}

	existTag.TagName = req.TagName

	return svc.db.WithContext(c).Save(existTag).Error
}

func (svc *wecomTagSvc) Delete(c *gin.Context, id uint) error {
	var existTag model.WecomTag

	err := svc.db.WithContext(c).Where("id = ?", id).First(&existTag).Error
	if errors.Is(err, gorm.ErrRecordNotFound) {
		return appError.NewErr("标签不存在")
	}

	if err != nil {
		return err
	}
	// Call WeCom API to sync the tag
	if err := svc.wecomClient.DeleteTag(int(existTag.ID)); err != nil {
		return err
	}

	return svc.db.WithContext(c).Delete(&existTag).Error
}

func (svc *wecomTagSvc) ListNoPage(c *gin.Context, tagName string) ([]model.WecomTag, error) {
	var tags []model.WecomTag
	query := svc.db.WithContext(c)
	if tagName != "" {
		query = query.Where("tag_name LIKE ?", "%"+tagName+"%")
	}
	err := query.Order("tag_name asc").Find(&tags).Error
	return tags, err
}

// SyncTagsFormWecom 从企微同步标签数据
// 临时接口，后续应该不用了
func (svc *wecomTagSvc) SyncTagsFormWecom(c *gin.Context) ([]model.WecomUserTag, error) {
	// 获取本地标签列表
	localTags, err := svc.ListNoPage(c, "")
	if err != nil {
		return nil, err
	}

	var allUserTags []model.WecomUserTag // 用于最终返回结果

	for _, tag := range localTags {
		members, err := svc.wecomClient.GetTagMembers(int(tag.ID))
		if err != nil {
			return nil, err
		}

		var batchUserTags []model.WecomUserTag
		for _, member := range members.UserList {
			var uid uint

			if strings.HasPrefix(member.UserID, "yx_") {
				uid = cast.ToUint(member.UserID[3:])
				if uid == 0 {
					continue // 跳过无效用户ID
				}
			} else {
				var existUser model.AdminUsers
				err := svc.db.WithContext(c).
					Where("qw_userid = ? AND status = 1", member.UserID).
					First(&existUser).Error

				if err != nil || existUser.ID == 0 {
					continue // 跳过不存在或无效用户
				}
				uid = existUser.ID
			}

			batchUserTags = append(batchUserTags, model.WecomUserTag{
				UserID:    uid,
				TagID:     tag.ID,
				CreatedAt: time.Now(), // 显式设置时间
			})
		}

		if len(batchUserTags) > 0 {
			// 使用冲突忽略策略批量插入
			result := svc.db.Clauses(clause.OnConflict{
				Columns: []clause.Column{
					{Name: "user_id"},
					{Name: "tag_id"}, // 联合主键冲突判断
				},
				DoNothing: true,
			}).CreateInBatches(batchUserTags, 100)

			if result.Error != nil {
				return nil, result.Error
			}

			// 记录成功插入的数据（可选）
			allUserTags = append(allUserTags, batchUserTags...)
		}
	}

	return allUserTags, nil
}

func (svc *wecomTagSvc) MoveDepartment(c *gin.Context, test string) (string, error) {
	// 获取所有的部门
	departments, err := svc.wecomClient.GetDepartmentSimpleList(1)
	if err != nil {
		return "", err
	}
	var returnStr string
	// 遍历部门，修改父ID
	i := 0
	fmt.Println(departments.Department)
	for _, department := range departments.Department {
		i++
		if department.ID == 1000756 {
			departmentID := department.ID
			err = svc.wecomClient.MoveDepartment(departmentID, 10)
			returnStr += ":" + cast.ToString(departmentID) + "\n"
		}

		/*		if department.ParentID == 2 {
					departmentID := department.ID
					var agency model.Agency
					err = svc.db.WithContext(c).Model(&model.Agency{}).Where("qw_partyid = ?", departmentID).First(&agency).Error
					if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
						return "", err
					}
					if departmentID == 2 {
						continue
					}
					if errors.Is(err, gorm.ErrRecordNotFound) {
						err = svc.wecomClient.MoveDepartment(departmentID, 2)
						if err != nil {
							return "", err
						}
						returnStr += agency.Name + ":" + cast.ToString(departmentID) + "\n"
						continue
					}
					if *agency.Channel == "agency" || *agency.Channel == "operator" {
						//更新部门
						err = svc.wecomClient.UpdateDepartment(departmentID, agency.Name, 10)
						if err != nil {
							return "", err
						}
						returnStr += agency.Name + ":" + cast.ToString(departmentID) + "\n"
					} else if *agency.Channel == "e_commerce" {
						//更新部门
						err = svc.wecomClient.UpdateDepartment(departmentID, agency.Name, 15)
						if err != nil {
							return "", err
						}
						returnStr += agency.Name + ":" + cast.ToString(departmentID) + "\n"
					} else if *agency.Channel == "aixue" {
						//更新部门
						err = svc.wecomClient.UpdateDepartment(departmentID, agency.Name, 30)
						if err != nil {
							return "", err
						}
						returnStr += agency.Name + ":" + cast.ToString(departmentID) + "\n"
					} else if *agency.Channel == "mengkubao" {
						//更新部门
						err = svc.wecomClient.UpdateDepartment(departmentID, agency.Name, 40)
						if err != nil {
							return "", err
						}
						returnStr += agency.Name + ":" + cast.ToString(departmentID) + "\n"
					} else if *agency.Channel == "other" {
						//更新部门
						err = svc.wecomClient.UpdateDepartment(departmentID, agency.Name, 20)
						if err != nil {
							return "", err
						}
						returnStr += agency.Name + ":" + cast.ToString(departmentID) + "\n"
					} else {
						//更新部门
						err = svc.wecomClient.UpdateDepartment(departmentID, agency.Name, 2)
						if err != nil {
							return "", err
						}
						returnStr += agency.Name + ":" + cast.ToString(departmentID) + "\n"
					}
				}
				if test == "1" && i >= 10 {
					fmt.Println(test)
					break
				}*/
	}
	return returnStr, nil
}
