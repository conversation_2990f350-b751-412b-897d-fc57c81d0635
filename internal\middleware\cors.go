package middleware

import (
	"github.com/gin-contrib/cors"
	"github.com/gin-gonic/gin"
	"time"
)

func Cors() gin.HandlerFunc {
	//跨域处理
	config := cors.Config{
		AllowOrigins: []string{"*"},
		AllowMethods: []string{"GET", "POST", "PUT", "PATCH", "DELETE", "HEAD", "OPTIONS"},
		AllowHeaders: []string{
			"Origin",
			"Content-Type",
			"Authorization",
			"X-Requested-With",
			"X-Gate-TypeName",
			"X-Gate-Type",
			"X-Request-ID",
		},
		ExposeHeaders:    []string{"Content-Length"},
		AllowCredentials: false,          // 允许携带认证信息
		MaxAge:           12 * time.Hour, // 预检请求结果缓存时间
	}
	return cors.New(config)
}
