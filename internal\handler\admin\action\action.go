package action

import (
	"github.com/gin-gonic/gin"
	"marketing/internal/api/action"
	"marketing/internal/handler"
	"marketing/internal/service"
	"strconv"
)

// HandlerAction   接口
type HandlerAction interface {
	// AuditAction 操作接口
	AuditAction(c *gin.Context)
	VerifyAction(c *gin.Context)
	RecordedAction(c *gin.Context)
	PrintFinishAction(c *gin.Context)
	FallbackAction(c *gin.Context)
	// GetPartitionTips 输入提示接口
	GetPartitionTips(c *gin.Context)
	GetTopAgencyTips(c *gin.Context)
	GetSecondAgencyTips(c *gin.Context)
	GetEndpointTips(c *gin.Context)
	// GetActionApplyInfo 查询接口
	GetActionApplyInfo(c *gin.Context)
	GetActionAuditInfo(c *gin.Context)
	GetActionVerifyInfo(c *gin.Context)
	GetActionFinishInfo(c *gin.Context)
	GetActionRecordedInfo(c *gin.Context)
	GetActionList(c *gin.Context)
}

type HandlerGormAction struct {
	svc service.ActionService
}

func (g *HandlerGormAction) GetActionRecordedInfo(c *gin.Context) {
	id, _ := strconv.Atoi(c.Param("id"))
	info, err := g.svc.GetActionRecordedInfo(c, id, 0)
	if err != nil {
		handler.Error(c, err)
		return
	}
	handler.Success(c, gin.H{
		"info": info,
	})
}

func (g *HandlerGormAction) FallbackAction(c *gin.Context) {
	id, _ := strconv.Atoi(c.Param("id"))
	if err := g.svc.FallbackAction(c, id); err != nil {
		handler.Error(c, err)
		return
	}
	handler.Success(c, gin.H{
		"res": true,
	})
}

func (g *HandlerGormAction) GetSecondAgencyTips(c *gin.Context) {
	agency, err := strconv.Atoi(c.Query("top_agency"))
	if err != nil {
		handler.Error(c, err)
		return
	}
	name := c.Query("name")
	list, err := g.svc.GetSecondAgencyTips(c, agency, name)
	if err != nil {
		handler.Error(c, err)
		return
	}
	handler.Success(c, gin.H{
		"list": list,
	})
}

func (g *HandlerGormAction) GetPartitionTips(c *gin.Context) {
	list, err := g.svc.GetPartitionTips(c)
	if err != nil {
		handler.Error(c, err)
		return
	}
	handler.Success(c, gin.H{
		"list": list,
	})
}

func (g *HandlerGormAction) GetTopAgencyTips(c *gin.Context) {
	partition, err := strconv.Atoi(c.Query("partition_id"))
	name := c.Query("name")
	list, err := g.svc.GetAgencyTips(c, partition, name)
	if err != nil {
		handler.Error(c, err)
		return
	}
	handler.Success(c, gin.H{
		"list": list,
	})
}

func (g *HandlerGormAction) GetEndpointTips(c *gin.Context) {
	agency, err := strconv.Atoi(c.Query("second_agency"))
	name := c.Query("name")
	list, err := g.svc.GetEndpointTips(c, agency, name)
	if err != nil {
		handler.Error(c, err)
		return
	}
	handler.Success(c, gin.H{
		"list": list,
	})
}

func (g *HandlerGormAction) PrintFinishAction(c *gin.Context) {
	id, err := strconv.Atoi(c.Param("id"))
	if err != nil {
		handler.Error(c, err)
		return
	}
	info, err := g.svc.PrintFinishAction(c, id)
	if err != nil {
		handler.Error(c, err)
		return
	}
	handler.Success(c, info)
}

func (g *HandlerGormAction) GetActionAuditInfo(c *gin.Context) {
	id, err := strconv.Atoi(c.Param("id"))
	if err != nil {
		handler.Error(c, err)
		return
	}
	info, err := g.svc.GetActionAuditInfo(c, id, 0)
	if err != nil {
		handler.Error(c, err)
		return
	}
	handler.Success(c, info)
}

func (g *HandlerGormAction) GetActionVerifyInfo(c *gin.Context) {
	id, err := strconv.Atoi(c.Param("id"))
	if err != nil {
		handler.Error(c, err)
		return
	}
	info, err := g.svc.GetActionVerifyInfo(c, id, 0)
	if err != nil {
		handler.Error(c, err)
		return
	}
	handler.Success(c, info)
}

// GetActionFinishInfo 查询完成信息
func (g *HandlerGormAction) GetActionFinishInfo(c *gin.Context) {
	id, err := strconv.Atoi(c.Param("id"))
	if err != nil {
		handler.Error(c, err)
		return
	}
	info, err := g.svc.GetActionFinishInfo(c, id, 0)
	if err != nil {
		handler.Error(c, err)
		return
	}
	handler.Success(c, info)
}

func (g *HandlerGormAction) GetActionList(c *gin.Context) {
	// 创建并初始化搜索参数结构体
	params := action.SearchActionParams{
		TypeName:  c.Query("type"),
		StartDate: c.Query("date_start"),
		EndDate:   c.Query("date_end"),
		Page:      1,  // 默认值
		Size:      20, // 默认值
	}

	// 解析整数类型参数
	if partition, err := strconv.Atoi(c.Query("dp")); err == nil {
		params.Partition = partition
	}

	if year, err := strconv.Atoi(c.Query("year")); err == nil {
		params.Year = strconv.Itoa(year)
	}

	if agency, err := strconv.Atoi(c.Query("second_agency")); err == nil {
		params.Agency = agency
	}

	if endpoint, err := strconv.Atoi(c.Query("endpoint")); err == nil {
		params.Endpoint = endpoint
	}

	if status, err := strconv.Atoi(c.Query("status")); err == nil {
		params.Status = status
	}

	if topAgency, err := strconv.Atoi(c.Query("top_agency")); err == nil {
		params.TopAgency = topAgency
	}

	// 解析分页参数
	if page, err := strconv.Atoi(c.Query("page")); err == nil && page > 0 {
		params.Page = page
	}

	if pageSize, err := strconv.Atoi(c.Query("page_size")); err == nil && pageSize > 0 {
		params.Size = pageSize
	}

	// 调用服务层方法
	list, total, page, pageSize, err := g.svc.SearchActionList(c, params)
	if err != nil {
		handler.Error(c, err)
		return
	}

	handler.Success(c, gin.H{
		"list":     list,
		"total":    total,
		"page":     page,
		"pageSize": pageSize,
	})
}

// GetActionApplyInfo 查询活动申请信息
func (g *HandlerGormAction) GetActionApplyInfo(c *gin.Context) {
	id, err := strconv.Atoi(c.Param("id"))
	if err != nil {
		handler.Error(c, err)
		return
	}
	info, err := g.svc.GetActionApplyInfo(c, id, 0)
	if err != nil {
		handler.Error(c, err)
		return
	}
	card, err := g.svc.GetActionCard(c, id, info.Type)
	handler.Success(c, gin.H{
		"apply": info,
		"card":  card,
	})
}

// AuditAction 审核活动
func (g *HandlerGormAction) AuditAction(c *gin.Context) {
	var audit action.AuditAction
	if err := c.ShouldBind(&audit); err != nil {
		handler.Error(c, err)
		return
	}
	audit.UID = c.GetUint("uid")
	if err := g.svc.AuditAction(c, audit); err != nil {
		handler.Error(c, err)
		return
	}
	handler.Success(c, nil)
}

// VerifyAction 验证活动
func (g *HandlerGormAction) VerifyAction(c *gin.Context) {
	var verify action.VerifyAction
	if err := c.ShouldBind(&verify); err != nil {
		handler.Error(c, err)
		return
	}
	if err := g.svc.VerifyAction(c, verify); err != nil {
		handler.Error(c, err)
		return
	}
	handler.Success(c, nil)
}

// RecordedAction  入账确认
func (g *HandlerGormAction) RecordedAction(c *gin.Context) {
	var account action.RecordedAction
	err := c.ShouldBind(&account)
	if err != nil {
		handler.Error(c, err)
		return
	}
	if err := g.svc.RecordedAction(c, account); err != nil {
		handler.Error(c, err)
		return
	}
	handler.Success(c, nil)
}

func NewHandlerGormAction(svc service.ActionService) HandlerAction {
	return &HandlerGormAction{svc: svc}
}
