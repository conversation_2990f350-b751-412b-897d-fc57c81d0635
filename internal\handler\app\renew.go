package app

import (
	"marketing/internal/api/renew"
	"marketing/internal/consts"
	"marketing/internal/handler"
	appError "marketing/internal/pkg/errors"
	service "marketing/internal/service/renew"

	"github.com/gin-gonic/gin"
	"github.com/spf13/cast"
)

type RenewAppHandler interface {
	GetMachine(c *gin.Context)
	Add(c *gin.Context)
	Update(c *gin.Context)
	Lists(c *gin.Context)
	GetInfo(c *gin.Context)
	Delete(c *gin.Context)
	GetIssue(c *gin.Context)
	GetStatus(c *gin.Context)
	GetWarranties(c *gin.Context)
}

type renewAppHandler struct {
	svc service.ApplicationServiceInterface
}

func NewRenewAppHandler(svc service.ApplicationServiceInterface) RenewAppHandler {
	return &renewAppHandler{
		svc: svc,
	}
}
func (h *renewAppHandler) GetMachine(c *gin.Context) {
	barcode := c.Query("barcode")
	if barcode == "" {
		handler.Error(c, appError.NewErr("barcode不能为空"))
		return
	}

	data, err := h.svc.GetMachine(c, barcode)
	if err != nil {
		handler.Error(c, err)
		return
	}
	handler.Success(c, data)
}
func (h *renewAppHandler) Add(c *gin.Context) {
	// 1. 参数校验
	var req renew.AddApplicationReq
	if err := c.ShouldBind(&req); err != nil {
		handler.Error(c, err)
		return
	}
	req.ApplicantType = consts.EndpointPrefix
	err := h.svc.Add(c, &req)
	if err != nil {
		handler.Error(c, err)
		return
	}
	handler.Success(c)
}

func (h *renewAppHandler) Update(c *gin.Context) {
	id := cast.ToUint(c.Param("id"))
	if id == 0 {
		handler.Error(c, appError.NewErr("ID不能为空"))
		return
	}
	req := &renew.AddApplicationReq{}
	if err := c.ShouldBind(req); err != nil {
		handler.Error(c, err)
		return
	}
	req.ID = id
	req.ApplicantType = consts.EndpointPrefix
	err := h.svc.Update(c, req)
	if err != nil {
		handler.Error(c, err)
		return
	}
	handler.Success(c)
}
func (h *renewAppHandler) Lists(c *gin.Context) {
	var req renew.ListApplicationReq
	if err := c.ShouldBind(&req); err != nil {
		handler.Error(c, err)
		return
	}
	req.SetDefaults()
	data, total, err := h.svc.Lists(c, &req)
	if err != nil {
		handler.Error(c, err)
		return
	}
	handler.Success(c, gin.H{
		"list":  data,
		"total": total,
	})
}

func (h *renewAppHandler) GetInfo(c *gin.Context) {
	id := cast.ToUint(c.Param("id"))
	if id == 0 {
		handler.Error(c, appError.NewErr("ID不能为空"))
		return
	}
	data, err := h.svc.GetInfo(c, id)
	if err != nil {
		handler.Error(c, err)
		return
	}
	handler.Success(c, data)
}
func (h *renewAppHandler) Delete(c *gin.Context) {
	id := cast.ToUint(c.Param("id"))
	if id == 0 {
		handler.Error(c, appError.NewErr("ID不能为空"))
		return
	}
	err := h.svc.Delete(c, id, consts.EndpointPrefix)
	if err != nil {
		handler.Error(c, err)
		return
	}
	handler.Success(c)
}
func (h *renewAppHandler) GetIssue(c *gin.Context) {
	data := consts.GetRenewIssues()
	handler.Success(c, data)
}

func (h *renewAppHandler) GetStatus(c *gin.Context) {
	data := consts.GetRenewStatusSlice()
	handler.Success(c, data)
}
func (h *renewAppHandler) GetWarranties(c *gin.Context) {
	barcode := c.Query("barcode")
	if barcode == "" {
		handler.Error(c, appError.NewErr("barcode不能为空"))
		return
	}

	data, err := h.svc.GetWarranties(c, barcode)
	if err != nil {
		handler.Error(c, err)
		return
	}
	handler.Success(c, data)
}
