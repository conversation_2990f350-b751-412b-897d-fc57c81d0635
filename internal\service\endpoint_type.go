package service

import (
	"marketing/internal/dao"
	"marketing/internal/model"
)

// EndpointTypeService 结构体用于处理业务逻辑
type EndpointTypeService struct {
	DAO *dao.EndpointTypeDao
}

// NewEndpointTypeService 创建新的 EndpointTypeService
func NewEndpointTypeService(dao *dao.EndpointTypeDao) *EndpointTypeService {
	return &EndpointTypeService{DAO: dao}
}

// GetAll 获取所有记录
func (service *EndpointTypeService) GetAll() ([]model.EndpointType, error) {
	return service.DAO.GetAll()
}

// GetByID 根据 ID 获取单个记录
func (service *EndpointTypeService) GetByID(id int) (*model.EndpointType, error) {
	return service.DAO.GetByID(id)
}

// Create 创建新的记录
func (service *EndpointTypeService) Create(endpointType *model.EndpointType) error {
	return service.DAO.Create(endpointType)
}

// Update 更新记录
func (service *EndpointTypeService) Update(id int, updatedEndpointType *model.EndpointType) (*model.EndpointType, error) {
	return service.DAO.Update(id, updatedEndpointType)
}

// Delete 将记录的 status 设置为 0
func (service *EndpointTypeService) Delete(id int) error {
	return service.DAO.Delete(id)
}

// Restore 将记录的 status 设置为 1
func (service *EndpointTypeService) Restore(id int) error {
	return service.DAO.Restore(id)
}
