package oss

import (
	"github.com/gogf/gf/text/gstr"
	"regexp"
)

var HttpPrefixReg = regexp.MustCompile(`(?i)^https?://`)

func AddOssBaseUrl(url string) string {
	// return fillUpUrl(viper.GetString("oss.baseUrl"), url)
	return url
}

func RemoveOssBaseUrl(url string) string {
	// return gstr.Replace(url, viper.GetString("oss.baseUrl"), "")
	return url
}

func fillUpUrl(prefix, url string) string {
	if url == "" {
		return url
	}

	if HttpPrefixReg.MatchString(url) {
		return url
	}

	return gstr.TrimRight(prefix, "/") + "/" + gstr.TrimLeft(url, "/")
}
