package action

import (
	"marketing/internal/api/action"
	"marketing/internal/consts"
	"marketing/internal/handler"
	myErrors "marketing/internal/pkg/errors"
	"marketing/internal/pkg/log"
	"marketing/internal/service"
	"os"
	"sort"
	"strconv"

	"github.com/gin-gonic/gin"
)

type PromotionHandle struct {
	promo service.PromotionService
}

// Add 添加活动
func (p *PromotionHandle) Add(c *gin.Context) {
	var param action.PromotionAddReq
	err := c.ShouldBind(&param)
	if err != nil {
		handler.Error(c, err)
		return
	}
	id, err := p.promo.Create(c, param)
	if err != nil {
		handler.Error(c, err)
		return
	}
	handler.Success(c, gin.H{
		"id": id,
	})
}

// Delete 批量删除
func (p *PromotionHandle) Delete(c *gin.Context) {
	var req struct {
		IDs []int `json:"ids"`
	}
	if err := c.ShouldBind(&req); err != nil {
		handler.Error(c, err)
		return
	}
	if len(req.IDs) == 0 {
		handler.Error(c, myErrors.NewErr("ids不能为空"))
	}
	err := p.promo.Delete(c, req.IDs)
	if err != nil {
		handler.Error(c, err)
		return
	}
	handler.Success(c, nil)
}

// Update 更新活动
func (p *PromotionHandle) Update(c *gin.Context) {
	var param action.PromotionUpdateReq
	err := c.ShouldBind(&param)
	if err != nil {
		handler.Error(c, err)
		return
	}
	err = p.promo.Update(c, param)
	if err != nil {
		handler.Error(c, err)
		return
	}
	handler.Success(c, nil)
}

// GetList 获取活动列表
func (p *PromotionHandle) GetList(c *gin.Context) {
	var param action.PromotionsReq
	if err := c.ShouldBind(&param); err != nil {
		handler.Error(c, err)
		return
	}
	param.SetDefaults()
	// 获取当前用户代理ID
	list, total, err := p.promo.GetList(c, param)
	if err != nil {
		handler.Error(c, err)
		return
	}
	handler.Success(c, gin.H{
		"list":      list,
		"page":      param.Page,
		"page_size": param.PageSize,
		"total":     total,
	})
}

// GetInfo 获取活动详情
func (p *PromotionHandle) GetInfo(c *gin.Context) {
	id := c.Param("id")
	info, err := p.promo.GetInfo(c, id)
	if err != nil {
		handler.Error(c, err)
		return
	}
	handler.Success(c, info)
}

// GetJoinList 获取参与名单列表
func (p *PromotionHandle) GetJoinList(c *gin.Context) {
	page, err := strconv.Atoi(c.DefaultQuery("page", "1"))
	pageSize, err := strconv.Atoi(c.DefaultQuery("page_size", "10"))
	agency := c.Query("agency_id")
	endpoint := c.Query("endpoint_id")
	model := c.Query("model")
	status := c.Query("status")
	id, err := strconv.Atoi(c.Query("promotion_id"))
	if err != nil {
		handler.Error(c, err)
		return
	}
	barcode := c.Query("barcode")
	list, total, err := p.promo.GetJoinList(c, id, page, pageSize, agency, endpoint, status, model, barcode)
	if err != nil {
		handler.Error(c, err)
		return
	}
	handler.Success(c, gin.H{
		"list":      list,
		"page":      page,
		"page_size": pageSize,
		"total":     total,
	})
}

// GetAgencyTips 获取代理提示
func (p *PromotionHandle) GetAgencyTips(c *gin.Context) {
	name := c.Query("name")
	list, err := p.promo.GetAgencyTips(c, name)
	if err != nil {
		handler.Error(c, err)
		return
	}
	handler.Success(c, list)
}

// GetEndpointTips 获取终端提示
func (p *PromotionHandle) GetEndpointTips(c *gin.Context) {
	name := c.Query("name")
	id := c.Query("agency_id")
	list, err := p.promo.GetEndpointTips(c, name, id)
	if err != nil {
		handler.Error(c, err)
		return
	}
	handler.Success(c, list)
}

// GetModelTips 获取机型提示
func (p *PromotionHandle) GetModelTips(c *gin.Context) {
	name := c.Query("name")
	list, err := p.promo.GetModelTips(c, name)
	if err != nil {
		handler.Error(c, err)
		return
	}
	handler.Success(c, list)
}

// GetJoinInfo 获取参与详情
func (p *PromotionHandle) GetJoinInfo(c *gin.Context) {
	id := c.Param("id")
	store, order, err := p.promo.GetJoinInfo(c, id)
	if err != nil {
		handler.Error(c, err)
		return
	}
	handler.Success(c, gin.H{
		"store": store,
		"order": order,
	})
}

// AddJoin 添加参与名单
func (p *PromotionHandle) AddJoin(c *gin.Context) {
	var result struct {
		Id      int    `json:"id" form:"id" binding:"required"`
		Barcode string `json:"barcode" form:"barcode" binding:"required"`
	}
	err := c.ShouldBind(&result)
	if result.Id == 0 || result.Barcode == "" {
		handler.Error(c, myErrors.NewErr("参数错误"))
		return
	}
	id, err := p.promo.AddJoin(c, result.Id, result.Barcode)
	if err != nil {
		handler.Error(c, err)
		return
	}
	handler.Success(c, gin.H{
		"id": id,
	})
}

// DeleteJoin 批量删除参与名单
func (p *PromotionHandle) DeleteJoin(c *gin.Context) {
	var req struct {
		Ids []int `json:"ids" form:"ids" binding:"required"`
	}
	err := c.ShouldBind(&req)
	if err != nil {
		handler.Error(c, err)
		return
	}

	err = p.promo.DeleteJoin(c, req.Ids)
	if err != nil {
		handler.Error(c, err)
		return
	}
	handler.Success(c, nil)
}

// SyncJoin 同步参与名单
func (p *PromotionHandle) SyncJoin(c *gin.Context) {
	id, err := strconv.Atoi(c.Param("id"))
	if err != nil {
		handler.Error(c, err)
		return
	}
	err = p.promo.SyncJoin(c, id)
	if err != nil {
		handler.Error(c, err)
		return
	}
	handler.Success(c, nil)
}

func (p *PromotionHandle) Export(c *gin.Context) {
	page, err := strconv.Atoi(c.DefaultQuery("page", "1"))
	pageSize, err := strconv.Atoi(c.DefaultQuery("page_size", "10"))
	agency := c.Query("agency_id")
	endpoint := c.Query("endpoint_id")
	model := c.Query("model_id")
	status := c.Query("status")
	id, err := strconv.Atoi(c.Query("promotion_id"))
	if err != nil {
		handler.Error(c, err)
		return
	}
	url, err := p.promo.Export(c, id, page, pageSize, agency, endpoint, model, status)
	if err != nil {
		handler.Error(c, err)
		return
	}
	defer func() {
		err := os.Remove(url)
		if err != nil {
			log.New().Error("[暑期促销活动]删除出错：	%v")
		}
	}()
	info, _ := p.promo.GetInfo(c, strconv.Itoa(id))
	var filename string
	if i, ok := info["name"].(string); ok {
		filename = i + "活动名单.xlsx"
	} else {
		filename = "活动名单.xlsx"
	}
	c.Writer.Header().Set("Content-Disposition", "attachment; filename="+filename)
	// 设置文件的 MIME 类型为 Excel 文件类型
	c.Writer.Header().Set("Content-Type", "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet")
	c.File(url)
}

func (p *PromotionHandle) UpdateReceipt(c *gin.Context) {
	var req action.UpdatePromotionReq
	err := c.ShouldBind(&req)
	if err != nil {
		handler.Error(c, err)
		return
	}
	err = p.promo.UpdateReceipt(c, req)
	if err != nil {
		handler.Error(c, err)
		return
	}
	handler.Success(c, nil)
}

func (p *PromotionHandle) FallBack(c *gin.Context) {
	id := c.Param("id")
	err := p.promo.FallBack(c, id)
	if err != nil {
		handler.Error(c, err)
		return
	}
	handler.Success(c, nil)
}

func (p *PromotionHandle) GetPromotionType(c *gin.Context) {
	data := consts.GetPromotionTypeMap()
	var res []map[string]interface{}
	for k, v := range data {
		res = append(res, map[string]interface{}{
			"value": k,
			"label": v,
		})
	}
	// 按 value 排序
	sort.Slice(res, func(i, j int) bool {
		return res[i]["value"].(uint8) < res[j]["value"].(uint8)
	})
	handler.Success(c, res)
}

// Barcode 名单查询
func (p *PromotionHandle) Barcode(c *gin.Context) {
	barcode := c.Query("barcode")
	if barcode == "" {
		handler.Error(c, myErrors.NewErr("条码不能为空"))
		return
	}

	data, err := p.promo.GetBarcodeInfo(c, barcode)
	if err != nil {
		handler.Error(c, err)
		return
	}

	handler.Success(c, data)
}

func NewPromotionHandle(promo service.PromotionService) *PromotionHandle {
	return &PromotionHandle{
		promo: promo,
	}
}
