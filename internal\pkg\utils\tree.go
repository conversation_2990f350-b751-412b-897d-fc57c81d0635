package utils

import "sort"

// TreeNode 接口定义
type TreeNode interface {
	GetID() uint
	GetParentID() uint
	GetRank() int
	AddChild(TreeNode)
	IsLeaf() bool
}

// BuildTree 构建树结构的函数
func BuildTree[T TreeNode](nodes []T) []T {
	if len(nodes) == 0 {
		return nil
	}

	sort.SliceStable(nodes, func(i, j int) bool {
		return nodes[i].GetRank() < nodes[j].GetRank()
	})

	nodeMap := make(map[uint]T)
	var roots []T

	for _, node := range nodes {
		nodeMap[node.GetID()] = node // Map 节点用于快速访问

		if node.GetParentID() == 0 {
			roots = append(roots, node) // 根节点直接追加
		} else if parent, exists := nodeMap[node.GetParentID()]; exists {
			if !parent.IsLeaf() {
				parent.AddChild(node) // 给父节点添加子节点
			}
		}
	}

	return roots
}
