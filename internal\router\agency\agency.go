package agency

import (
	"github.com/gin-gonic/gin"
	"marketing/internal/dao"
	"marketing/internal/handler/agency"
	"marketing/internal/service"
)

type TAgencyRouter struct {
}

func NewAgencyRouter() *TAgencyRouter {
	return &TAgencyRouter{}
}

func (m *TAgencyRouter) Register(r *gin.RouterGroup) {
	// 代理
	agencyRouter := r.Group("/agency")
	{
		agencyDao := dao.NewAgencyDao()
		partitionDao := dao.NewPartitionDao()
		kingDeeDao := dao.NewKingDeeAgencyDao()
		channelDao := dao.NewChannelDao()
		agencyService := service.NewAgencyService(agencyDao, partitionDao, kingDeeDao, channelDao)
		agencyController := agency.NewAgency(agencyService)
		agencyRouter.GET("/list", agencyController.AgencyList)
		agencyRouter.POST("/edit", agencyController.EditAgency)
		agencyRouter.DELETE("/delete", agencyController.DeleteAgency)
	}
}
