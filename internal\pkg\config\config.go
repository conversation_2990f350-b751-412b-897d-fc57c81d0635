package config

import (
	"fmt"
	"log"

	"github.com/fsnotify/fsnotify"
	"github.com/spf13/viper"
	//"marketing/internal/pkg/log"
)

func Init() {

	//配置文件
	viper.SetConfigName("config") // 配置文件的文件名（不需要扩展名）
	viper.SetConfigType("yaml")   // 配置文件的类型（可以是 yaml、json、toml 等）
	viper.AddConfigPath("./conf") // 可选：指定配置文件的搜索路径（默认为当前目录）
	viper.AddConfigPath("../../conf")

	err := viper.ReadInConfig()
	if err != nil {
		log.Fatal("read config failed", err)
	}
	fmt.Println(viper.Get("server.mode"))
	viper.WatchConfig()

	viper.OnConfigChange(func(e fsnotify.Event) {
		log.Fatal("read config failed", err)
	})
	fmt.Println("config init success...")
}
