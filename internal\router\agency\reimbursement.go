package agency

import (
	"marketing/internal/handler/agency/reimbursement"

	"github.com/gin-gonic/gin"
)

type ReimbursementRouter struct {
	reimbursementHandler reimbursement.ReimbursementHandler
}

func NewReimbursementRouter() *ReimbursementRouter {
	reimbursementHandler := reimbursement.NewReimbursement()

	return &ReimbursementRouter{
		reimbursementHandler: reimbursementHandler,
	}
}

func (rr *ReimbursementRouter) Register(r *gin.RouterGroup) {
	// 首页
	r.GET("/homepage", rr.reimbursementHandler.GetHomePage)
	// 客户端汇总
	r.GET("/client-summary", rr.reimbursementHandler.GetClientSummary)
	// 客户端订单
	r.GET("/client-orders", rr.reimbursementHandler.GetClientOrders)
	// 订单详情
	r.GET("/promotional-products/detail", rr.reimbursementHandler.GetPromotionalProductsDetail)
	// 广告费用详情
	r.GET("/advert-expense/detail", rr.reimbursementHandler.GetAdvertExpenseDetail)
	// 订单详情
	r.GET("/client-detail", rr.reimbursementHandler.GetClientDetail)
	// 政策详情
	r.GET("/policy-detail", rr.reimbursementHandler.GetPolicyDetail)
	// 广告费政策申请
	r.POST("/advert-expense/apply", rr.reimbursementHandler.ApplyAdvertExpense)
}
