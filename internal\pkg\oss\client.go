package oss

import (
	"github.com/aliyun/alibabacloud-oss-go-sdk-v2/oss"
	"github.com/aliyun/alibabacloud-oss-go-sdk-v2/oss/credentials"
	"github.com/spf13/viper"
)

func NewClient() *oss.Client {
	region := viper.GetString("oss.region")
	var provider = credentials.NewEnvironmentVariableCredentialsProvider()
	// 创建OSS客户端配置
	cfg := oss.LoadDefaultConfig().
		WithCredentialsProvider(provider).
		WithRegion(region)

	// 创建OSS客户端
	client := oss.NewClient(cfg)
	return client
}
