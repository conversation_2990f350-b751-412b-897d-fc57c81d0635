package action

import (
	"github.com/gin-gonic/gin"
	"marketing/internal/api/action"
	"marketing/internal/handler"
	"marketing/internal/service"
	"strconv"
)

type HandlerActionAgencyImpl interface {
	// ApplyAction 操作接口
	ApplyAction(ctx *gin.Context)
	DropDown(c *gin.Context)
	DeleteAction(ctx *gin.Context)
	FinishAction(c *gin.Context)
	UpdateAction(c *gin.Context)
	// GetTypeTips 查询接口
	GetTypeTips(c *gin.Context)
	GetEndpointTips(c *gin.Context)
	GetTopAgencyTips(c *gin.Context)
	GetSecondAgencyTips(c *gin.Context)
	GetPartitionTips(c *gin.Context)
	//GetActionList 信息接口
	GetActionList(c *gin.Context)
	GetActionApplyInfo(c *gin.Context)
	GetActionFinishInfo(c *gin.Context)
	GetActionAuditInfo(c *gin.Context)
	GetActionVerifyInfo(c *gin.Context)
	GetActionRecordedInfo(c *gin.Context)
}
type HandleActionAgency struct {
	svc service.ActionService
}

func (g *HandleActionAgency) GetActionRecordedInfo(c *gin.Context) {
	id, _ := strconv.Atoi(c.Param("id"))
	uid := c.GetUint("uid")
	uidInt := uid
	info, err := g.svc.GetActionRecordedInfo(c, id, int(uidInt))
	if err != nil {
		handler.Error(c, err)
		return
	}
	handler.Success(c, gin.H{
		"info": info,
	})
}

func (g *HandleActionAgency) GetActionApplyInfo(c *gin.Context) {
	id, err := strconv.Atoi(c.Param("id"))
	if err != nil {
		handler.Error(c, err)
		return
	}
	uid := c.GetUint("uid")
	info, err := g.svc.GetActionApplyInfo(c, id, int(uid))
	if err != nil {
		handler.Error(c, err)
		return
	}
	handler.Success(c, gin.H{
		"apply": info,
	})
}

func (g *HandleActionAgency) GetActionFinishInfo(c *gin.Context) {
	id, err := strconv.Atoi(c.Param("id"))
	if err != nil {
		handler.Error(c, err)
		return
	}
	uid := c.GetUint("uid")
	info, err := g.svc.GetActionFinishInfo(c, id, int(uid))
	if err != nil {
		handler.Error(c, err)
		return
	}
	handler.Success(c, info)
}

func (g *HandleActionAgency) GetActionAuditInfo(c *gin.Context) {
	id, err := strconv.Atoi(c.Param("id"))
	if err != nil {
		handler.Error(c, err)
		return
	}
	uid := c.GetUint("uid")
	info, err := g.svc.GetActionAuditInfo(c, id, int(uid))
	if err != nil {
		handler.Error(c, err)
		return
	}
	handler.Success(c, info)
}

func (g *HandleActionAgency) GetActionVerifyInfo(c *gin.Context) {
	id, err := strconv.Atoi(c.Param("id"))
	if err != nil {
		handler.Error(c, err)
		return
	}
	uid := c.GetUint("uid")
	info, err := g.svc.GetActionVerifyInfo(c, id, int(uid))
	if err != nil {
		handler.Error(c, err)
		return
	}
	handler.Success(c, info)
}

func (g *HandleActionAgency) GetActionList(c *gin.Context) {
	// 创建并初始化搜索参数结构体
	params := action.SearchActionParams{
		TypeName:  c.Query("type"),
		StartDate: c.Query("date_start"),
		EndDate:   c.Query("date_end"),
		Page:      1,  // 默认值
		Size:      20, // 默认值
	}

	// 解析整数类型参数
	if partition, err := strconv.Atoi(c.Query("dp")); err == nil {
		params.Partition = partition
	}

	if year, err := strconv.Atoi(c.Query("year")); err == nil {
		params.Year = strconv.Itoa(year)
	}

	if agency, err := strconv.Atoi(c.Query("second_agency")); err == nil {
		params.Agency = agency
	}

	if endpoint, err := strconv.Atoi(c.Query("endpoint")); err == nil {
		params.Endpoint = endpoint
	}

	if topAgency, err := strconv.Atoi(c.Query("top_agency")); err == nil {
		params.TopAgency = topAgency
	}

	// 解析分页参数
	if page, err := strconv.Atoi(c.Query("page")); err == nil && page > 0 {
		params.Page = page
	}

	if pageSize, err := strconv.Atoi(c.Query("page_size")); err == nil && pageSize > 0 {
		params.Size = pageSize
	}

	// 设置用户ID
	params.UID = c.GetUint("uid")

	// 调用服务层方法
	list, total, page, pageSize, err := g.svc.SearchActionList(c, params)
	if err != nil {
		handler.Error(c, err)
		return
	}

	handler.Success(c, gin.H{
		"list":     list,
		"total":    total,
		"page":     page,
		"pageSize": pageSize,
	})
}

func (g *HandleActionAgency) GetSecondAgencyTips(c *gin.Context) {
	agency, err := strconv.Atoi(c.Query("top_agency"))
	if err != nil {
		handler.Error(c, err)
		return
	}
	list, err := g.svc.GetSecondAgencyTips(c, agency, "")
	if err != nil {
		handler.Error(c, err)
		return
	}
	handler.Success(c, gin.H{
		"list": list,
	})
}

func (g *HandleActionAgency) GetPartitionTips(c *gin.Context) {
	list, err := g.svc.GetPartitionTips(c)
	if err != nil {
		handler.Error(c, err)
		return
	}
	handler.Success(c, gin.H{
		"list": list,
	})
}

func (g *HandleActionAgency) GetTopAgencyTips(c *gin.Context) {
	partition, err := strconv.Atoi(c.Query("partition_id"))
	list, err := g.svc.GetAgencyTips(c, partition, "")
	if err != nil {
		handler.Error(c, err)
		return
	}
	handler.Success(c, gin.H{
		"list": list,
	})
}

func (g *HandleActionAgency) GetEndpointTips(c *gin.Context) {
	agency, err := strconv.Atoi(c.Query("second_agency"))
	list, err := g.svc.GetEndpointTips(c, agency, "")
	if err != nil {
		handler.Error(c, err)
		return
	}
	handler.Success(c, gin.H{
		"list": list,
	})
}

func (g *HandleActionAgency) GetTypeTips(c *gin.Context) {
	name := c.Query("name")
	list, err := g.svc.GetTypeTips(c, name)
	if err != nil {
		handler.Error(c, err)
		return
	}
	handler.Success(c, gin.H{
		"list": list,
	})
}

// DropDown 头部字段
func (g *HandleActionAgency) DropDown(c *gin.Context) {
	list, err := g.svc.GetDropDown(c)
	if err != nil {
		handler.Error(c, err)
		return
	}
	handler.Success(c, gin.H{
		"list": list,
	})
}

// ApplyAction  申请活动
func (g *HandleActionAgency) ApplyAction(c *gin.Context) {
	var act action.ApplyActionV1
	if err := c.ShouldBind(&act); err != nil {
		handler.Error(c, err)
		return
	}
	id, err := g.svc.ApplyActionV1(c, act)
	if err != nil {
		handler.Error(c, err)
		return
	}
	handler.Success(c, gin.H{
		"id": id,
	})
}

// DeleteAction 删除活动
func (g *HandleActionAgency) DeleteAction(c *gin.Context) {
	id, err := strconv.Atoi(c.Param("id"))
	if err != nil {
		handler.Error(c, err)
		return
	}
	if err := g.svc.DeleteAction(c, id); err != nil {
		handler.Error(c, err)
		return
	}
	handler.Success(c, nil)
}

// UpdateAction 修改活动
func (g *HandleActionAgency) UpdateAction(c *gin.Context) {
	var apply action.UpdateAction
	if err := c.ShouldBindJSON(&apply); err != nil {
		handler.Error(c, err)
		return
	}
	uid := c.GetUint("uid")
	if err := g.svc.UpdateAction(c, uid, apply); err != nil {
		handler.Error(c, err)
		return
	}
	handler.Success(c, nil)
}

// FinishAction 完成活动
func (g *HandleActionAgency) FinishAction(c *gin.Context) {
	var finish action.FinishAction
	if err := c.ShouldBind(&finish); err != nil {
		handler.Error(c, err)
		return
	}
	if err := g.svc.FinishAction(c, finish); err != nil {
		handler.Error(c, err)
		return
	}
	handler.Success(c, gin.H{
		"res": "ok",
	})
}
func NewHandleActionAgency(svc service.ActionService) HandlerActionAgencyImpl {
	return &HandleActionAgency{svc: svc}
}
