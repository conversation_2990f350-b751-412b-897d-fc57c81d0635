package model

type AcDevicesUniq struct {
	ID         int    `json:"id"`
	Series     int    `json:"series"`
	ModelID    int    `json:"model_id"`
	Model      string `json:"model"`
	NavModel   string `json:"nav_model"`
	Uniqno     string `json:"uniqno"`
	Barcode    string `json:"barcode"`
	Number     string `json:"number"`
	Imei       string `json:"imei"`
	Province   string `json:"province"`
	City       string `json:"city"`
	District   string `json:"district"`
	Address    string `json:"address"`
	Adcode     string `json:"adcode"`
	Area       string `json:"area"`
	Subarea    string `json:"subarea"`
	AgencyID   int    `json:"agency_id"`
	AgencyName string `json:"agency_name"`
	MesExists  int    `json:"mes_exists"`
	Stored     int    `json:"stored"`
	CustCode   string `json:"cust_code"`
	CustName   string `json:"cust_name"`
	Channel    string `json:"channel"`
	BillDate   string `json:"bill_date"`
	Exchanged  int    `json:"exchanged"`
	Createat   string `json:"createat"`
	Status     int    `json:"status"`
	DeletedAt  string `json:"deleted_at"`
	Bindat     string `json:"bindat"`
	Location   string `json:"location"`
	Ip         string `json:"ip"`
	Origin     string `json:"origin"`
	Uid        int    `json:"uid"`
	Did        int    `json:"did"`
}

func (a *AcDevicesUniq) TableName() string {
	return "ac_devices_uniq"
}
