package redis

import (
	"context"
	"marketing/internal/pkg/log"

	"github.com/redis/go-redis/v9"
	"github.com/spf13/viper"
	"go.uber.org/zap"
)

func NewRedis() *redis.Client {
	Client := redis.NewClient(&redis.Options{
		Username: viper.GetString("redis.username"),
		Addr:     viper.GetString("redis.addr"),
		Password: viper.GetString("redis.password"), // 没有密码，默认值
		DB:       viper.GetInt("redis.db"),          // 默认DB 0
	})
	if err := Client.Ping(context.Background()).Err(); err != nil {
		log.Fatal("redis ping err", zap.Error(err))
	}
	return Client
}
