package warranty

import (
	"github.com/gin-gonic/gin"
	warranty "marketing/internal/service"
)

type WarrantyExchangeHandler interface {
	Index(c *gin.Context)
	Store(c *gin.Context)
	Show(c *gin.Context)
	Update(c *gin.Context)
	Delete(c *gin.Context)
}

type warrantyExchangeHandler struct {
	warrantyService warranty.InterfaceWarranty
}

func (w warrantyExchangeHandler) Index(c *gin.Context) {
	//TODO implement me
	panic("implement me")
}

func (w warrantyExchangeHandler) Store(c *gin.Context) {
	//TODO implement me
	panic("implement me")
}

func (w warrantyExchangeHandler) Show(c *gin.Context) {
	//TODO implement me
	panic("implement me")
}

func (w warrantyExchangeHandler) Update(c *gin.Context) {
	//TODO implement me
	panic("implement me")
}

func (w warrantyExchangeHandler) Delete(c *gin.Context) {
	//TODO implement me
	panic("implement me")
}
