package endpoint

import (
	"github.com/gin-gonic/gin"
	"marketing/internal/handler"
	"marketing/internal/model"
	"marketing/internal/service"
	"net/http"
	"strconv"
)

// EndpointTypeController 结构体
type EndpointTypeController struct {
	Service *service.EndpointTypeService
}

var ETypeController EndpointTypeController

// NewEndpointTypeController 创建新的 EndpointTypeController
func NewEndpointTypeController(service *service.EndpointTypeService) *EndpointTypeController {
	return &EndpointTypeController{Service: service}
}

// GetAll 获取所有记录
func (ctrl *EndpointTypeController) GetAll(c *gin.Context) {
	endpointTypes, err := ctrl.Service.GetAll()
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}
	// 返回结果
	handler.Success(c, gin.H{
		"data": endpointTypes,
	})
}

// GetByID 根据 ID 获取单个记录
func (ctrl *EndpointTypeController) GetByID(c *gin.Context) {
	id, _ := strconv.Atoi(c.Param("id"))
	endpointType, err := ctrl.Service.GetByID(id)
	if err != nil {
		c.JSON(http.StatusNotFound, gin.H{"error": "EndpointType not found"})
		return
	}
	handler.Success(c, gin.H{
		"data": endpointType,
	})
}

// Create 创建新的记录
func (ctrl *EndpointTypeController) Create(c *gin.Context) {
	var input model.EndpointType
	if err := c.ShouldBindJSON(&input); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}
	if err := ctrl.Service.Create(&input); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}
	handler.Success(c, gin.H{
		"data": input,
	})
}

// Update 更新记录
func (ctrl *EndpointTypeController) Update(c *gin.Context) {
	id, _ := strconv.Atoi(c.Param("id"))
	var input model.EndpointType
	if err := c.ShouldBindJSON(&input); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	updatedEndpointType, err := ctrl.Service.Update(id, &input)
	if err != nil {
		c.JSON(http.StatusNotFound, gin.H{"error": "EndpointType not found"})
		return
	}
	handler.Success(c, gin.H{
		"data": updatedEndpointType,
	})
}

// Delete 删除记录（设置 status 为 0）
func (ctrl *EndpointTypeController) Delete(c *gin.Context) {
	id, _ := strconv.Atoi(c.Param("id"))
	if err := ctrl.Service.Delete(id); err != nil {
		c.JSON(http.StatusNotFound, gin.H{"error": "EndpointType not found"})
		return
	}
	handler.Success(c, gin.H{
		"message": "Deleted successfully",
	})
}

// Restore 恢复记录（设置 status 为 1）
func (ctrl *EndpointTypeController) Restore(c *gin.Context) {
	id, _ := strconv.Atoi(c.Param("id"))
	if err := ctrl.Service.Restore(id); err != nil {
		c.JSON(http.StatusNotFound, gin.H{"error": "EndpointType not found"})
		return
	}
	handler.Success(c, gin.H{
		"message": "Restored successfully",
	})
}
