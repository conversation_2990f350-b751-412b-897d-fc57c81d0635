// Package router 该包主要负责定义和管理与路由相关的逻辑，
// 本文件 agency.go 具体用于配置和注册代理端（agency）相关的路由组。
// 它通过路由构建器和中间件包装器，灵活地为不同路由配置不同的中间件。
package router

import (
	"marketing/internal/config"
	"marketing/internal/consts"
	"marketing/internal/middleware"
	"marketing/internal/router/agency"
	"marketing/internal/service/auth"
	"marketing/internal/service/system"

	"github.com/gin-gonic/gin"
)

// NewAgencyRouterGroup 创建代理端路由组
func NewAgencyRouterGroup(authService auth.ServiceInterface,
	adminUserService system.AdminUserInterface,
	cfg *config.Config,
	mineRouter *agency.MineRouter,
	baseRouter *agency.BaseRouter,
	reimbursementRouter *agency.ReimbursementRouter,
) IRouterGroup {
	// 基础中间件
	baseMiddleware := []gin.HandlerFunc{
		middleware.AuthToken(authService),
	}

	// 需要权限校验的中间件
	permissionMiddleware := []gin.HandlerFunc{
		middleware.AuthToken(authService),
		middleware.CheckAdminPermission(authService),
		middleware.CheckTokenSystem(authService, consts.AgencyPrefix),
	}

	// 对于调试模式，可以选择跳过权限中间件
	if cfg.Server.Mode == "debug" {
		permissionMiddleware = []gin.HandlerFunc{}
		baseMiddleware = []gin.HandlerFunc{}
	}

	// 使用路由构建器构建路由组
	builder := NewRouterBuilder("agency")

	// 添加需要权限校验的路由
	builder.AddRouterWithMiddleware(agency.NewActionRouter(adminUserService), permissionMiddleware...)
	builder.AddRouterWithMiddleware(agency.NewRenewRouter(), permissionMiddleware...)
	builder.AddRouterWithPathAndMiddleware(reimbursementRouter, "/reimbursement", permissionMiddleware...)

	// 添加只需基本权限验证的路由
	builder.AddRouterWithMiddleware(baseRouter, baseMiddleware...)
	builder.AddRouterWithMiddleware(mineRouter, baseMiddleware...)

	// 构建并返回路由组
	return builder.Build()
}
