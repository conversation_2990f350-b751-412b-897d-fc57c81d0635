package dao

import (
	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
	"marketing/internal/model"
	"marketing/internal/pkg/db"
	"marketing/internal/pkg/utils"
)

type MachineAccessoryRelationDao interface {
	CreateMachineAccessoryRelation(c *gin.Context, relation *model.MachineAccessoryRelation) error
	DeleteMachineAccessoryRelation(c *gin.Context, modelId, accessoryId int) error
	GetMachineAccessoryRelation(c *gin.Context, modelId, accessoryId int) *model.MachineAccessoryRelation
	GetMachineAccessoryRelationList(c *gin.Context, modelId, pageNum, pageSize int) ([]*model.MachineAccessoryRelation, int64)
	GetMachineAccessoryRelationByMid(c *gin.Context, modelId int) (list []*model.MachineAccessoryRelation)
}

// MachineAccessoryRelationDaoImpl 实现 MachineAccessoryRelationDao 接口
type MachineAccessoryRelationDaoImpl struct {
	db *gorm.DB
}

// NewMachineAccessoryRelationDao 创建 MachineAccessoryRelationDao 实例
func NewMachineAccessoryRelationDao() MachineAccessoryRelationDao {
	return &MachineAccessoryRelationDaoImpl{
		db: db.GetDB(""),
	}
}

func (d *MachineAccessoryRelationDaoImpl) CreateMachineAccessoryRelation(c *gin.Context, relation *model.MachineAccessoryRelation) error {
	return d.db.WithContext(c).Create(relation).Error
}

func (d *MachineAccessoryRelationDaoImpl) DeleteMachineAccessoryRelation(c *gin.Context, modelId, accessoryId int) error {
	return d.db.WithContext(c).
		Delete(&model.MachineAccessoryRelation{}, "model_id = ? and accessory_id = ?", modelId, accessoryId).Error
}

func (d *MachineAccessoryRelationDaoImpl) GetMachineAccessoryRelation(c *gin.Context, modelId, accessoryId int) *model.MachineAccessoryRelation {
	var relation model.MachineAccessoryRelation
	err := d.db.WithContext(c).Model(&model.MachineAccessoryRelation{}).
		Where("model_id = ? and accessory_id = ?", modelId, accessoryId).
		First(&relation).Error
	if err != nil {
		return nil
	}
	return &relation
}

func (d *MachineAccessoryRelationDaoImpl) GetMachineAccessoryRelationList(c *gin.Context, modelId, pageNum, pageSize int) ([]*model.MachineAccessoryRelation, int64) {
	query := d.db.WithContext(c).Model(&model.MachineAccessoryRelation{})

	if modelId > 0 {
		query = query.Where("model_id = ?", modelId)
	}

	data, total := utils.PaginateQueryV1(query.Order("updated_at desc"), pageNum, pageSize, new([]*model.MachineAccessoryRelation))
	for i, p := range *data {
		(*data)[i].CreateTime = utils.GetTimeStr(p.CreatedAt)
		(*data)[i].UpdateTime = utils.GetTimeStr(p.UpdatedAt)
	}

	return *data, total
}

func (d *MachineAccessoryRelationDaoImpl) GetMachineAccessoryRelationByMid(c *gin.Context, modelId int) (list []*model.MachineAccessoryRelation) {
	d.db.WithContext(c).Model(&model.MachineAccessoryRelation{}).Where("model_id = ?", modelId).Find(&list)

	for _, l := range list {
		l.CreateTime = utils.GetTimeStr(l.CreatedAt)
		l.UpdateTime = utils.GetTimeStr(l.UpdatedAt)
	}

	return
}
