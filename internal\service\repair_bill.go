package service

import (
	"errors"
	"fmt"
	"github.com/gin-gonic/gin"
	"github.com/xuri/excelize/v2"
	"marketing/internal/dao"
	"marketing/internal/dao/admin_user"
	"marketing/internal/model"
	"marketing/internal/pkg/utils"
)

type RepairBillSvc interface {
	GetRepairBillList(c *gin.Context, param dao.GetRepairBillListParam) ([]*model.RepairBillDetails, int64)
	GetRepairBill(c *gin.Context, id int) *model.RepairBillDetails
	EditRepairBill(c *gin.Context, param model.RepairBill, malfunctionIds []int, accessoryParam []*dao.RepairBillMachineAccessoryParam) error
	DeleteRepairBill(c *gin.Context, id int) error
	ExportRepairBillList(c *gin.Context, param dao.GetRepairBillListParam) *excelize.File
}

type RepairBillSvcImpl struct {
	userRepo               admin_user.UserDao
	afterSalesEndpointRepo dao.AfterSalesEndpointDao
	machineMalfunctionRepo dao.MachineMalfunctionDao
	machineAccessoryRepo   dao.MachineAccessoryRelationDao
	machineTypeRepo        dao.MachineTypeDao
	repairBillRepo         dao.RepairBillDao
}

func NewRepairBillService(
	userRepo admin_user.UserDao,
	endpointRepo dao.AfterSalesEndpointDao,
	malfunctionRepo dao.MachineMalfunctionDao,
	machineAccessoryRepo dao.MachineAccessoryRelationDao,
	machineTypeRepo dao.MachineTypeDao,
	repairBillRepo dao.RepairBillDao) RepairBillSvc {

	return &RepairBillSvcImpl{
		userRepo:               userRepo,
		afterSalesEndpointRepo: endpointRepo,
		machineMalfunctionRepo: malfunctionRepo,
		machineAccessoryRepo:   machineAccessoryRepo,
		machineTypeRepo:        machineTypeRepo,
		repairBillRepo:         repairBillRepo,
	}
}

func (s *RepairBillSvcImpl) GetRepairBillList(c *gin.Context, param dao.GetRepairBillListParam) ([]*model.RepairBillDetails, int64) {
	repairBills, total := s.repairBillRepo.GetRepairBillList(c, param)

	uIds := make([]uint, 0)
	endpointIds := make([]int, 0)
	modelIds := make([]int, 0)
	repairBillIds := make([]int, 0)

	for _, r := range repairBills {
		uIds = append(uIds, uint(r.CreateUid))
		endpointIds = utils.AddIntArrayValue(endpointIds, r.EndpointId)
		modelIds = utils.AddIntArrayValue(modelIds, utils.StringToInt(r.ModelId))
		repairBillIds = utils.AddIntArrayValue(repairBillIds, r.Id)
	}

	userList, _ := s.userRepo.GetByIDs(c, uIds)
	for _, r := range repairBills {
		for _, user := range userList {
			if r.CreateUid == int(user.ID) {
				r.Name = user.Name
				break
			}
		}
	}

	endpointList := s.afterSalesEndpointRepo.GetAfterSalesEndpointByIds(c, endpointIds)
	for _, r := range repairBills {
		for _, e := range endpointList {
			if r.EndpointId == e.Id {
				r.EndpointName = e.Name
				r.EndpointPhone = e.Phone
				r.EndpointAddress = e.Address
			}
			break
		}
	}

	machineTypeList := s.machineTypeRepo.GetMachineTypeByModelIds(c, modelIds)
	for _, r := range repairBills {
		for _, m := range machineTypeList {
			if utils.StringToInt(r.ModelId) == m.ModelId {
				r.ModelName = m.Name
				break
			}
		}
	}

	malfunctionIds := make([]int, 0)
	rs := s.repairBillRepo.GetMachineMalfunctionRelationList(c, repairBillIds)
	for _, r := range rs {
		malfunctionIds = utils.AddIntArrayValue(malfunctionIds, r.MalfunctionTop)
		malfunctionIds = utils.AddIntArrayValue(malfunctionIds, r.MalfunctionId)
	}

	malfunctionList := s.machineMalfunctionRepo.GetMachineMalfunctionByIds(c, malfunctionIds)
	for _, repairBill := range repairBills {
		repairBill.MalfunctionList = make([]model.RepairBillMachineMalfunctionInfo, 0)

		for _, r := range rs {
			if repairBill.Id == r.RepairBillId {
				malfunctionInfo := model.RepairBillMachineMalfunctionInfo{}

				for _, m := range malfunctionList {
					if malfunctionInfo.MalfunctionLocId > 0 && malfunctionInfo.MalfunctionTypeId > 0 {
						break
					}

					if r.MalfunctionTop == m.Id {
						malfunctionInfo.MalfunctionLocId = m.Id
						malfunctionInfo.MalfunctionLocName = m.Title
					}

					if r.MalfunctionId == m.Id {
						malfunctionInfo.MalfunctionTypeId = m.Id
						malfunctionInfo.MalfunctionTypeName = m.Title
					}
				}

				repairBill.MalfunctionList = append(repairBill.MalfunctionList, malfunctionInfo)
			}
		}
	}

	accessoryList := s.repairBillRepo.GetMachineAccessoryList(c, repairBillIds)
	for _, repairBill := range repairBills {
		repairBill.AccessoryList = make([]model.RepairBillMachineAccessoryInfo, 0)
		for _, a := range accessoryList {
			if repairBill.Id == a.RepairBillId {
				repairBill.AccessoryList = append(repairBill.AccessoryList, *a)
			}
		}
	}

	return repairBills, total
}

func (s *RepairBillSvcImpl) GetRepairBill(c *gin.Context, id int) *model.RepairBillDetails {
	repairBill := s.repairBillRepo.GetRepairBillById(c, id)
	if repairBill == nil {
		return nil
	}

	user, _ := s.userRepo.GetByID(c, uint(repairBill.CreateUid))
	if user != nil {
		repairBill.Name = user.Name
	}

	endpoint := s.afterSalesEndpointRepo.GetAfterSalesEndpointById(c, repairBill.EndpointId)
	if endpoint != nil {
		repairBill.EndpointName = endpoint.Name
		repairBill.EndpointPhone = endpoint.Phone
		repairBill.EndpointAddress = endpoint.Address
	}

	machineTypeList := s.machineTypeRepo.GetMachineTypeByModelIds(c, []int{utils.StringToInt(repairBill.ModelId)})
	for _, m := range machineTypeList {
		if utils.StringToInt(repairBill.ModelId) == m.ModelId {
			repairBill.ModelName = m.Name
			break
		}
	}

	malfunctionIds := make([]int, 0)
	rs := s.repairBillRepo.GetMachineMalfunctionRelationList(c, []int{repairBill.Id})
	for _, r := range rs {
		malfunctionIds = utils.AddIntArrayValue(malfunctionIds, r.MalfunctionTop)
		malfunctionIds = utils.AddIntArrayValue(malfunctionIds, r.MalfunctionId)
	}

	repairBill.MalfunctionList = make([]model.RepairBillMachineMalfunctionInfo, 0)
	malfunctionList := s.machineMalfunctionRepo.GetMachineMalfunctionByIds(c, malfunctionIds)
	for _, r := range rs {
		if repairBill.Id == r.RepairBillId {
			malfunctionInfo := model.RepairBillMachineMalfunctionInfo{}

			for _, m := range malfunctionList {
				if malfunctionInfo.MalfunctionLocId > 0 && malfunctionInfo.MalfunctionTypeId > 0 {
					break
				}

				if r.MalfunctionTop == m.Id {
					malfunctionInfo.MalfunctionLocId = m.Id
					malfunctionInfo.MalfunctionLocName = m.Title
				}

				if r.MalfunctionId == m.Id {
					malfunctionInfo.MalfunctionTypeId = m.Id
					malfunctionInfo.MalfunctionTypeName = m.Title
				}
			}

			repairBill.MalfunctionList = append(repairBill.MalfunctionList, malfunctionInfo)
		}
	}

	accessoryList := s.repairBillRepo.GetMachineAccessoryList(c, []int{repairBill.Id})
	repairBill.AccessoryList = make([]model.RepairBillMachineAccessoryInfo, 0)
	for _, a := range accessoryList {
		if repairBill.Id == a.RepairBillId {
			repairBill.AccessoryList = append(repairBill.AccessoryList, *a)
		}
	}

	return repairBill
}

func (s *RepairBillSvcImpl) EditRepairBill(c *gin.Context, param model.RepairBill, malfunctionIds []int, accessoryParam []*dao.RepairBillMachineAccessoryParam) error {
	repairBill := s.repairBillRepo.GetRepairBillById(c, param.Id)
	if repairBill == nil {
		return errors.New("编辑维修工作单:维修工作单不存在")
	}

	machineType := s.machineTypeRepo.GetMachineTypeByModelId(c, utils.StringToInt(param.ModelId))
	if machineType == nil {
		return errors.New("编辑维修工作单:模型不存在")
	}

	uMap := make(map[string]interface{}, 0)
	uMap["sn_code"] = param.SnCode
	uMap["buy_date"] = param.BuyDate
	uMap["customer_name"] = param.CustomerName
	uMap["customer_phone"] = param.CustomerPhone
	uMap["model_id"] = param.ModelId
	uMap["receive_machine_date"] = param.ReceiveMachineDate
	uMap["repair_source"] = param.RepairSource
	uMap["description"] = param.Description
	uMap["repair_reason_type"] = param.RepairReasonType
	uMap["repair_type"] = param.RepairType
	uMap["repair_deal"] = param.RepairDeal
	uMap["express_date"] = param.ExpressDate
	uMap["express_number"] = param.ExpressNumber
	uMap["remark"] = param.Remark
	uMap["is_checked"] = param.IsChecked

	hasMalfunctionIds := make([]int, 0)
	malfunctionList := s.machineMalfunctionRepo.GetMachineMalfunctionByIds(c, malfunctionIds)
	for _, m := range malfunctionList {
		hasMalfunctionIds = append(hasMalfunctionIds, m.Id)
	}

	for _, malfunctionId := range malfunctionIds {
		if !utils.IntHas(malfunctionId, hasMalfunctionIds) {
			return errors.New("编辑维修工作单:故障类型不存在")
		}
	}

	hasAccessoryIds := make([]int, 0)
	accessoryList := s.machineAccessoryRepo.GetMachineAccessoryRelationByMid(c, utils.StringToInt(param.ModelId))
	for _, a := range accessoryList {
		hasAccessoryIds = append(hasAccessoryIds, a.Id)
	}

	repairCost := float64(0)

	for _, a := range accessoryParam {
		noExist := true
		for _, accessory := range accessoryList {
			if a.Id == accessory.Id {
				repairCost = utils.Float64Add(repairCost, float64(a.Amount)*accessory.Price)
				noExist = false
				break
			}
		}

		if noExist {
			return errors.New("编辑维修工作单:配件不存在")
		}

	}

	uMap["repair_cost"] = repairCost

	return s.repairBillRepo.EditRepairBill(c, repairBill.Id, uMap, malfunctionList, accessoryParam)
}

func (s *RepairBillSvcImpl) DeleteRepairBill(c *gin.Context, id int) error {
	return s.repairBillRepo.DeleteRepairBill(c, id)
}

func (s *RepairBillSvcImpl) ExportRepairBillList(c *gin.Context, param dao.GetRepairBillListParam) *excelize.File {
	list, _ := s.GetRepairBillList(c, param)

	sheetName := "维修工作单表"

	// 新建一个excel文件,并添加表
	file := excelize.NewFile()
	_, _ = file.NewSheet(sheetName)

	// 设置所有列居中
	style, _ := file.NewStyle(&excelize.Style{
		Alignment: &excelize.Alignment{
			Horizontal: "center",
			Vertical:   "center",
		},
		Font: &excelize.Font{
			Size: 12,
		},
	})

	_ = file.SetCellValue(sheetName, "A1", "工单编号")
	_ = file.SetCellStyle(sheetName, "A1", "A1", style)
	_ = file.SetColWidth(sheetName, "A", "A", 25.0)
	_ = file.SetCellValue(sheetName, "B1", "条码")
	_ = file.SetCellStyle(sheetName, "B1", "B1", style)
	_ = file.SetColWidth(sheetName, "B", "B", 25.0)
	_ = file.SetCellValue(sheetName, "C1", "处理人员")
	_ = file.SetCellStyle(sheetName, "C1", "C1", style)
	_ = file.SetColWidth(sheetName, "C", "C", 15.0)
	_ = file.SetCellValue(sheetName, "D1", "维修点名称")
	_ = file.SetCellStyle(sheetName, "D1", "D1", style)
	_ = file.SetColWidth(sheetName, "D", "D", 50.0)
	_ = file.SetCellValue(sheetName, "E1", "维修点地址")
	_ = file.SetCellStyle(sheetName, "E1", "E1", style)
	_ = file.SetColWidth(sheetName, "E", "E", 50.0)
	_ = file.SetCellValue(sheetName, "F1", "维修点电话")
	_ = file.SetCellStyle(sheetName, "F1", "F1", style)
	_ = file.SetColWidth(sheetName, "F", "F", 25.0)
	_ = file.SetCellValue(sheetName, "G1", "创建日期")
	_ = file.SetCellStyle(sheetName, "G1", "G1", style)
	_ = file.SetColWidth(sheetName, "G", "G", 25.0)

	for i, l := range list {
		index := i + 2
		_ = file.SetCellValue(sheetName, fmt.Sprintf("A%d", index), l.BillNumber)
		_ = file.SetCellStyle(sheetName, fmt.Sprintf("A%d", index), fmt.Sprintf("A%d", index), style)
		_ = file.SetCellValue(sheetName, fmt.Sprintf("B%d", index), l.SnCode)
		_ = file.SetCellStyle(sheetName, fmt.Sprintf("B%d", index), fmt.Sprintf("B%d", index), style)
		_ = file.SetCellValue(sheetName, fmt.Sprintf("C%d", index), l.Name)
		_ = file.SetCellStyle(sheetName, fmt.Sprintf("C%d", index), fmt.Sprintf("C%d", index), style)
		_ = file.SetCellValue(sheetName, fmt.Sprintf("D%d", index), l.EndpointName)
		_ = file.SetCellStyle(sheetName, fmt.Sprintf("D%d", index), fmt.Sprintf("D%d", index), style)
		_ = file.SetCellValue(sheetName, fmt.Sprintf("E%d", index), l.EndpointAddress)
		_ = file.SetCellStyle(sheetName, fmt.Sprintf("E%d", index), fmt.Sprintf("E%d", index), style)
		_ = file.SetCellValue(sheetName, fmt.Sprintf("F%d", index), l.EndpointPhone)
		_ = file.SetCellStyle(sheetName, fmt.Sprintf("F%d", index), fmt.Sprintf("F%d", index), style)
		_ = file.SetCellValue(sheetName, fmt.Sprintf("G%d", index), l.CreateTime)
		_ = file.SetCellStyle(sheetName, fmt.Sprintf("G%d", index), fmt.Sprintf("G%d", index), style)
	}

	// 这一步是删除默认创建的Sheet1表
	_ = file.DeleteSheet("Sheet1")

	return file
}
