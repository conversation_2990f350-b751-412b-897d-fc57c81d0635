package third_party

import (
	"fmt"
	api "marketing/internal/api/third_party"
	"marketing/internal/handler"
	"marketing/internal/pkg/aes"
	"marketing/internal/pkg/errors"
	"marketing/internal/pkg/utils"
	"marketing/internal/service/third_party"
	"net/http"

	"github.com/gin-gonic/gin"
	"github.com/spf13/cast"
)

type CustomerServiceHandler interface {
	SendCaptcha(c *gin.Context)
	QueryRepairProgress(c *gin.Context)
	RepairUrgent(c *gin.Context) // 寄修进度加急
	Test(c *gin.Context)         // 测试方法
}
type customerServiceHandler struct {
	customerService third_party.CustomerServiceInterface
}

// NewCustomerServiceHandler 创建客户服务处理器实例
func NewCustomerServiceHandler(customerService third_party.CustomerServiceInterface) CustomerServiceHandler {
	return &customerServiceHandler{
		customerService: customerService,
	}
}

// SendCaptcha 发送验证码
func (h *customerServiceHandler) SendCaptcha(c *gin.Context) {
	// SendSms 短信发送
	var req api.SendCaptchaRequest
	if err := c.ShouldBind(&req); err != nil {
		handler.RespondWithCode(c, http.StatusBadRequest, err)
		return
	}

	// 验证手机号格式
	if !utils.IsValidPhone(req.Phone) {
		handler.RespondWithCode(c, http.StatusBadRequest, errors.NewErr("电话格式错误"))
		return
	}
	template := cast.ToInt(req.Template)

	if template < 1 || template > 5 {
		handler.RespondWithCode(c, http.StatusBadRequest, errors.NewErr("模板参数错误"))
		return
	}

	// 调用发送短信服务
	err := h.customerService.SendCaptcha(c, req.Phone, template)
	if err != nil {
		handler.RespondWithCode(c, http.StatusInternalServerError, "发送短信失败: "+err.Error())
		return
	}

	handler.RespondWithCode(c, http.StatusOK, gin.H{"result": "success", "message": "短信已发送"})
}

// QueryRepairProgress 语音播报查询寄修进度
func (h *customerServiceHandler) QueryRepairProgress(c *gin.Context) {
	// 从中间件获取解密后的参数
	var keyword string

	// 解密
	queryParams := c.Request.URL.Query()
	aesKey := c.GetString("appkey")
	data, err := aes.DecryptAESParams(queryParams, aesKey, []string{"keyword", "phone", "appid"})

	if err != nil {
		handler.RespondWithCode(c, http.StatusBadRequest, gin.H{"msg": "参数解密失败", "ok": 0})
		return
	}
	keyword = data["keyword"]
	if keyword == "" {
		keyword = data["phone"]
	}

	// 验证关键字格式
	if keyword == "" || (len(keyword) != 6 && len(keyword) != 11) {
		handler.RespondWithCode(c, http.StatusBadRequest, gin.H{"msg": "寄修单号后六位或者手机号输入有误", "ok": 0})
		return
	}

	// 调用查询寄修进度服务
	progress := h.customerService.QueryRepairProgress(c, keyword)

	// 直接返回JSON字符串
	handler.RespondWithCode(c, http.StatusOK, progress)
}

// RepairUrgent 寄修进度加急
func (h *customerServiceHandler) RepairUrgent(c *gin.Context) {
	// 从中间件获取解密后的参数
	var keyword string

	// 解密
	queryParams := c.Request.URL.Query()
	aesKey := c.GetString("appkey")
	data, err := aes.DecryptAESParams(queryParams, aesKey, []string{"keyword", "phone", "appid"})
	if err != nil {
		handler.RespondWithCode(c, http.StatusBadRequest, gin.H{"msg": "参数解密失败", "ok": 0})
		return
	}
	keyword = data["keyword"]
	if keyword == "" {
		keyword = data["phone"]
	}

	// 验证关键字格式
	if keyword == "" || (len(keyword) != 6 && len(keyword) != 11) {
		handler.RespondWithCode(c, http.StatusBadRequest, gin.H{"msg": "寄修单号后六位或者手机号输入有误", "ok": 0})
		return
	}

	// 调用查询寄修进度服务
	progress := h.customerService.RepairUrgent(c, keyword)

	// 直接返回JSON字符串
	handler.RespondWithCode(c, http.StatusOK, progress)
}

func (h *customerServiceHandler) Test(c *gin.Context) {
	//加密
	fmt.Println("=====")
	fmt.Println(utils.MD5("appid=LsMQPiCqhXEE5Gx3tNIxFQ==&phone=Sa0UAlXs4h+qYH6+kQ66yg==&keyword=" + "3x9FgH7qR2TpL6yK"))
	//fmt.Println(aes.AesEncrypt("400-hollycrm", "3x9FgH7qR2TpL6yK"))
	fmt.Println("=====")
	// 测试方法，返回简单的JSON响应
	//data, err := aes.DecryptAESParams(c.Request.URL.Query(), "3x9FgH7qR2TpL6yK", []string{"appid"})
	//fmt.Println("Decrypted Data:", data, "Error:", err)
}
