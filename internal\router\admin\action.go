package admin

import (
	"marketing/internal/dao"
	"marketing/internal/handler/admin/action"
	"marketing/internal/pkg/db"
	"marketing/internal/pkg/oss"
	"marketing/internal/pkg/redis"
	"marketing/internal/pkg/upload"
	"marketing/internal/service"
	"marketing/internal/service/cache"

	"github.com/gin-gonic/gin"
)

type ActionRouter struct{}

func NewActionRouter() *ActionRouter {
	return &ActionRouter{}
}

func (a *ActionRouter) Register(r *gin.RouterGroup) {
	//活动管理模块
	activityRouter := r.Group("/activity")
	{
		client := oss.NewClient()
		uploadService := upload.NewOssUploadServiceV2(client)
		//cache 活动缓存
		RedisCache := redis.NewRedis()
		redisCache := cache.NewActionRedisCache(RedisCache)
		//关于活动类型的接口
		actionDao := dao.NewActionTypeGorm()
		actionService := service.NewGormActionTypeService(actionDao, redisCache)
		actionController := action.NewHandleActionType(actionService)
		activityRouter.POST("/type/save", actionController.Upsert)
		activityRouter.POST("/type/update", actionController.Update)
		activityRouter.GET("/type/update_status", actionController.ReviseTypeStatus)
		//查询活动类型
		activityRouter.GET("/type/list", actionController.GetActionTypeList)
		activityRouter.GET("/type/info/:id", actionController.GetByID)
		activityRouter.GET("/type/drop_down_all", actionController.DropDownAll)
		activityRouter.GET("/type/search", actionController.SearchType)
		//输入提示信息
		activityRouter.GET("/type/tips/type", actionController.GetActionTypeTips)
		activityRouter.GET("/type/tips/agency", actionController.GetAgencyTips)
		//弃用
		activityRouter.GET("/type/update_name", actionController.ReviseTypeName)
		//关于活动的接口
		activityDao := dao.NewActionGorm(uploadService)
		commonDao := dao.NewCommonGorm(db.GetDB())
		activityService := service.NewGormActionService(activityDao, actionDao, redisCache, commonDao)
		activityController := action.NewHandlerGormAction(activityService)
		activityRouter.POST("/action/audit", activityController.AuditAction)
		activityRouter.POST("/action/verify", activityController.VerifyAction)
		activityRouter.POST("/action/recorded", activityController.RecordedAction)
		activityRouter.GET("/action/fallback/:id", activityController.FallbackAction)
		//关于活动信息的查询接口
		activityRouter.GET("/action/list", activityController.GetActionList)
		activityRouter.GET("/action/apply_info/:id", activityController.GetActionApplyInfo)
		activityRouter.GET("/action/finish_info/:id", activityController.GetActionFinishInfo)
		activityRouter.GET("/action/audit_info/:id", activityController.GetActionAuditInfo)
		activityRouter.GET("/action/verify_info/:id", activityController.GetActionVerifyInfo)
		activityRouter.GET("/action/recorded_info/:id", activityController.GetActionRecordedInfo)
		activityRouter.GET("/action/print_finish/:id", activityController.PrintFinishAction)
		//下拉提示信息
		activityRouter.GET("/action/tips/partition", activityController.GetPartitionTips)
		activityRouter.GET("/action/tips/top_agency", activityController.GetTopAgencyTips)
		activityRouter.GET("/action/tips/second_agency", activityController.GetSecondAgencyTips)
		activityRouter.GET("/action/tips/endpoint", activityController.GetEndpointTips)
	}
	//促销活动
	PromotionRoute := r.Group("/promotion")
	{
		PromotionDao := dao.NewGormPromotionDao(db.DB)
		CommonDao := dao.NewCommonGorm(db.DB)
		PromotionService := service.NewGormPromotionService(PromotionDao, CommonDao)
		promotionController := action.NewPromotionHandle(PromotionService)
		// 促销-操作接口
		PromotionRoute.POST("/add", promotionController.Add)
		PromotionRoute.POST("/update", promotionController.Update)
		PromotionRoute.DELETE("/delete", promotionController.Delete)
		PromotionRoute.POST("/update_receipt", promotionController.UpdateReceipt)
		// 促销-	查询接口
		PromotionRoute.GET("/list", promotionController.GetList)
		PromotionRoute.GET("/info/:id", promotionController.GetInfo)
		// 搜索条件
		PromotionRoute.GET("/tips/agency", promotionController.GetAgencyTips)
		PromotionRoute.GET("/tips/endpoint", promotionController.GetEndpointTips)
		PromotionRoute.GET("/tips/model", promotionController.GetModelTips)
		PromotionRoute.GET("/tips/type", promotionController.GetPromotionType)
		// 名单-查询接口
		PromotionRoute.GET("/join/list", promotionController.GetJoinList)
		PromotionRoute.GET("/join/info/:id", promotionController.GetJoinInfo)
		// 名单-操作接口
		PromotionRoute.POST("/join/add", promotionController.AddJoin)
		PromotionRoute.DELETE("/join/delete", promotionController.DeleteJoin)
		PromotionRoute.GET("/join/export", promotionController.Export)
		PromotionRoute.GET("/fallback/:id", promotionController.FallBack)
		PromotionRoute.GET("/barcode", promotionController.Barcode)
	}
}
