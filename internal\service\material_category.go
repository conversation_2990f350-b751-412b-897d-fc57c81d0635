package service

import (
	"errors"
	"github.com/gin-gonic/gin"
	"marketing/internal/dao"
	"marketing/internal/model"
	"marketing/internal/pkg/utils"
	"strconv"
	"strings"
	"sync"
)

type MaterialCategoryInfo struct {
	Id        uint                   `json:"id"`         // 物料id
	Name      string                 `json:"name"`       // 物料名称
	Num       int                    `json:"num"`        // 物料数量
	ChildList []MaterialCategoryInfo `json:"child_list"` // 子标签列表
}

var (
	materialCategoryArray      []MaterialCategoryInfo
	materialCategoryArrayMutex sync.RWMutex
)

type MaterialCategorySvcInterface interface {
	GetAllMaterialCategory(c *gin.Context) (infos []MaterialCategoryInfo)
	RefreshMaterialCategory(c *gin.Context) (infos []MaterialCategoryInfo)
	EditMaterialCategory(c *gin.Context, id, pId int, name string) error
	AddMaterialCategory(c *gin.Context, pId int, name string) error
	DelMaterialCategory(c *gin.Context, id int) error
}

type MaterialCategorySvc struct {
	materialRepo dao.MaterialDao
	categoryRepo dao.MaterialCategoryDao
}

func NewMaterialCategoryService(materialRepo dao.MaterialDao, categoryRepo dao.MaterialCategoryDao) MaterialCategorySvcInterface {
	svc := &MaterialCategorySvc{
		materialRepo: materialRepo,
		categoryRepo: categoryRepo,
	}

	svc.RefreshMaterialCategory(new(gin.Context))

	return svc
}

func (s *MaterialCategorySvc) GetAllMaterialCategory(c *gin.Context) (infos []MaterialCategoryInfo) {
	materialCategoryArrayMutex.RLock()
	defer materialCategoryArrayMutex.RUnlock()
	return materialCategoryArray
}

func (s *MaterialCategorySvc) RefreshMaterialCategory(c *gin.Context) (infos []MaterialCategoryInfo) {
	materialCategoryArrayMutex.Lock()
	defer materialCategoryArrayMutex.Unlock()

	materialCategoryArray = make([]MaterialCategoryInfo, 0)
	categoryList := s.categoryRepo.GetAllMaterialCategory(c)
	materialNumList := s.materialRepo.CountMaterialNumGbyCategory(c)
	for _, category := range categoryList {
		for _, materialNum := range materialNumList {
			if int(category.Id) == materialNum.Category {
				category.Num = materialNum.Num
				break
			}
		}

		ids, err := s.getCategoryParentIds(category.FullPath)
		if err != nil {
			continue
		}

		if len(ids) > 0 {
			noExist := true
			for _, id := range ids {
				noExist = true
				for _, ent := range categoryList {
					if id == ent.Id {
						noExist = false
						break
					}
				}

				if noExist {
					break
				}
			}

			if noExist {
				// 从这里返回说明ids中有不存在的父标签id,直接跳过
				continue
			}
		}

		_ = s.addMaterialCategory(category)
	}

	return materialCategoryArray
}

func (s *MaterialCategorySvc) EditMaterialCategory(c *gin.Context, id, pId int, name string) error {
	materialCategoryArrayMutex.Lock()
	defer materialCategoryArrayMutex.Unlock()

	if len(name) == 0 {
		return errors.New("编辑物料标签:名称为空")
	}

	category := s.categoryRepo.GetMaterialCategoryById(c, id)
	if category == nil {
		return errors.New("编辑物料标签:标签不存在")
	}

	remove, fullPath := pId != category.Pid, category.FullPath
	uMap := make(map[string]interface{})

	if pId == category.Pid {
		category.Name = name
		uMap["name"] = category.Name
	} else if len(s.categoryRepo.GetMaterialCategoryListByPid(c, int(category.Id))) == 0 {
		parentCategory := s.categoryRepo.GetMaterialCategoryById(c, pId)
		if parentCategory == nil {
			return errors.New("编辑物料标签:父标签不存在")
		}

		category.Name = name
		category.Pid = int(parentCategory.Id)
		category.Level = parentCategory.Level + 1
		if parentCategory.Level == 1 {
			category.FullPath = utils.IntToString(int(parentCategory.Id))
		} else {
			category.FullPath = parentCategory.FullPath + "," + utils.IntToString(int(parentCategory.Id))
		}

		uMap["name"] = category.Name
		uMap["pid"] = category.Pid
		uMap["level"] = category.Level
		uMap["full_path"] = category.FullPath
	} else {
		return errors.New("编辑物料标签:不能移动非叶子标签")
	}

	// 修改DB
	dbErr := s.categoryRepo.UpdateMaterialCategory(c, int(category.Id), uMap)
	if dbErr != nil {
		return nil
	}

	// 移动旧的节点
	if remove {
		ids, err := s.getCategoryParentIds(fullPath)
		if err != nil {
			return errors.New("编辑物料标签:父id异常")
		}

		s.delChildCategory(ids, &materialCategoryArray, category.Id)
	}

	return s.addMaterialCategory(category)
}

func (s *MaterialCategorySvc) AddMaterialCategory(c *gin.Context, pId int, name string) error {
	materialCategoryArrayMutex.Lock()
	defer materialCategoryArrayMutex.Unlock()

	if len(name) == 0 {
		return errors.New("添加物料标签:名称为空")
	}

	category := &model.MaterialCategory{
		Name:  name,
		Pid:   pId,
		Level: 1,
	}

	if pId != 0 {
		parentCategory := s.categoryRepo.GetMaterialCategoryById(c, pId)
		if parentCategory == nil {
			return errors.New("添加物料标签:父标签不存在")
		}

		category.Level = parentCategory.Level + 1
		if parentCategory.Level == 1 {
			category.FullPath = utils.IntToString(int(parentCategory.Id))
		} else {
			category.FullPath = parentCategory.FullPath + "," + utils.IntToString(int(parentCategory.Id))
		}
	}

	dbErr := s.categoryRepo.CreateMaterialCategory(c, category)
	if dbErr != nil {
		return dbErr
	}

	return s.addMaterialCategory(category)
}

func (s *MaterialCategorySvc) DelMaterialCategory(c *gin.Context, id int) error {
	materialCategoryArrayMutex.Lock()
	defer materialCategoryArrayMutex.Unlock()

	category := s.categoryRepo.GetMaterialCategoryById(c, id)
	if category == nil {
		return errors.New("删除物料标签:标签不存在")
	}

	dbErr := s.categoryRepo.DeleteMaterialCategory(c, id)
	if dbErr != nil {
		return dbErr
	}

	ids, err := s.getCategoryParentIds(category.FullPath)
	if err != nil {
		return errors.New("删除物料标签:父id异常")
	}

	s.delChildCategory(ids, &materialCategoryArray, uint(id))

	return nil
}

func (s *MaterialCategorySvc) addMaterialCategory(category *model.MaterialCategory) error {
	ids, err := s.getCategoryParentIds(category.FullPath)
	if err != nil {
		return errors.New("添加物料标签:父id异常")
	}

	s.addChildCategory(ids, &materialCategoryArray, category)

	return nil
}

func (s *MaterialCategorySvc) addChildCategory(parentIds []uint, categoryArray *[]MaterialCategoryInfo, category *model.MaterialCategory) int {
	if len(parentIds) > 0 {
		for i, c := range *categoryArray {
			if c.Id == parentIds[0] {
				num := s.addChildCategory(parentIds[1:], &(*categoryArray)[i].ChildList, category)
				(*categoryArray)[i].Num += num
				return num
			}
		}

		parentCategory := MaterialCategoryInfo{
			Id:        parentIds[0],
			ChildList: make([]MaterialCategoryInfo, 0),
		}

		// 加入子标签
		parentCategory.Num = s.addChildCategory(parentIds[1:], &parentCategory.ChildList, category)
		// 加入父标签
		*categoryArray = append(*categoryArray, parentCategory)

		return parentCategory.Num
	}

	for i, c := range *categoryArray {
		if c.Id == category.Id {
			oldNum := (*categoryArray)[i].Num
			(*categoryArray)[i].Name = category.Name
			(*categoryArray)[i].Num = category.Num
			for _, childCategory := range (*categoryArray)[i].ChildList {
				(*categoryArray)[i].Num += childCategory.Num
			}
			return (*categoryArray)[i].Num - oldNum
		}
	}

	*categoryArray = append(*categoryArray, MaterialCategoryInfo{
		Id:        category.Id,
		Name:      category.Name,
		Num:       category.Num,
		ChildList: make([]MaterialCategoryInfo, 0),
	})

	return category.Num
}

func (s *MaterialCategorySvc) delChildCategory(parentIds []uint, categoryArray *[]MaterialCategoryInfo, id uint) int {
	if len(parentIds) > 0 {
		num := 0

		for i, c := range *categoryArray {
			if c.Id == parentIds[0] {
				num = s.delChildCategory(parentIds[1:], &(*categoryArray)[i].ChildList, id)
				(*categoryArray)[i].Num -= num
			}
		}

		return num
	}

	num := 0
	childList := make([]MaterialCategoryInfo, 0)
	for _, c := range *categoryArray {
		if c.Id == id {
			num = c.Num
			continue
		}
		childList = append(childList, c)
	}

	*categoryArray = childList

	return num
}

func (s *MaterialCategorySvc) getCategoryParentIds(path string) ([]uint, error) {
	if path == "" {
		return nil, nil
	}

	ids := make([]uint, 0)

	parentIds := strings.Split(path, ",")
	for _, parentId := range parentIds {
		if parentId == "" {
			continue
		}

		pid, err := strconv.Atoi(parentId)
		if err != nil {
			return nil, err
		}

		ids = append(ids, uint(pid))
	}

	return ids, nil
}
