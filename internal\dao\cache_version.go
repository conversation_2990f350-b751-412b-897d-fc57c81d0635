package dao

import (
	"gorm.io/gorm"
	"marketing/internal/model"
	"marketing/internal/pkg/db"
)

const (
	CvArticleCategory    = "article_category"
	CvMachineMalfunction = "machine_malfunction"
)

func GetCacheVersion(key string) int {
	var c model.CacheVersion
	err := db.GetDB("").Model(&model.CacheVersion{}).Where("cache_key = ?", key).First(&c).Error
	if err != nil {
		return 0
	}
	return c.Version
}

func AddCacheVersion(key string) error {
	return db.GetDB("").Model(&model.CacheVersion{}).Where("cache_key = ?", key).
		UpdateColumn("version", gorm.Expr("version + ?", 1)).Error
}
