//go:build wireinject
// +build wireinject

package main

import (
	"marketing/internal/config"
	"marketing/internal/handler/auth"
	"marketing/internal/handler/base"
	"marketing/internal/provider"
	"marketing/internal/router"
	"marketing/internal/router/admin"
	"marketing/internal/router/agency"
	"marketing/internal/router/app"
	svcauth "marketing/internal/service/auth"
	"marketing/internal/service/system"

	"github.com/google/wire"
)

func InitializeServer(cfg *config.Config) (*router.Router, error) {
	wire.Build(
		// 其他依赖
		provider.InfrastructureSet,
		provider.DaoSet,
		provider.ServiceSet,
		provider.HandlerSet,
		RouterSet,
	)
	// 可以在这里添加错误处理逻辑
	return nil, nil
}

var RouterSet = wire.NewSet(
	// auth路由
	router.NewAuthRouter,

	// 管理端子路由
	admin.NewBaseRouter,
	admin.NewEndpointRouter,
	admin.NewMineRouter,
	admin.NewSystemRouter,
	admin.NewAgencyRouter,
	admin.NewAfterSalesRouter,
	admin.NewReportRouter,

	//代理端子路由
	agency.NewMineRouter,
	agency.NewBaseRouter,
	agency.NewReimbursementRouter,

	//app端子路由
	app.NewMkbRouter,

	// 提供路由组切片和路由器
	provideRouter,
)

// 直接提供Router，避免通过IRouterGroup接口
func provideRouter(
	authHandler *auth.Auth,
	captchaHandler base.CaptchaHandler,
	endpointRouter *admin.EndpointRouter,
	systemRouter *admin.SystemRouter,
	agencyRouter *admin.AgencyRouter,
	afterSalesRouter *admin.AfterSalesRouter,
	baseRouter *admin.BaseRouter,
	mineRouter *admin.MineRouter,
	agencyMineRouter *agency.MineRouter,
	mkbRouter *app.MkbRouter,
	cfg *config.Config,
	authService svcauth.ServiceInterface,
	adminUserService system.AdminUserInterface,
	reportRouter *admin.ReportRouter,
	systemAppSvc system.AppSystemSvc,
	baseAgencyRouter *agency.BaseRouter,
	reimbursementRouter *agency.ReimbursementRouter,
) *router.Router {
	// 创建各个路由组
	authGroup := router.NewAuthRouterGroup(authHandler, captchaHandler)
	adminGroup := router.NewAdminRouterGroup(endpointRouter, systemRouter, agencyRouter, cfg, authService, afterSalesRouter, baseRouter, mineRouter, reportRouter)
	agencyGroup := router.NewAgencyRouterGroup(authService, adminUserService, cfg, agencyMineRouter, baseAgencyRouter, reimbursementRouter)
	appGroup := router.NewAppRouterGroup(authService, cfg, mkbRouter, systemAppSvc)
	thirdPartyGroup := router.NewThirdPartyRoutes(cfg)

	// 创建路由组列表
	groups := []router.IRouterGroup{
		authGroup,
		adminGroup,
		agencyGroup,
		appGroup,
		thirdPartyGroup,
	}

	// 创建并返回路由器
	return router.NewRouter(groups)
}
