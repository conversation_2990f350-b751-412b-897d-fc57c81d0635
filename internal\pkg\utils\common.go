package utils

import (
	"crypto/md5"
	"crypto/rand"
	"encoding/hex"
	"encoding/json"
	"fmt"
	"log"
	rand2 "math/rand"
	"regexp"
	"strings"
	"time"
)

// StructToJSONString converts a struct to a JSON string.
// If pretty is true, it returns a pretty-printed JSON string.
func StructToJSONString(v any, pretty bool) string {
	var jsonData []byte
	var err error

	if pretty {
		jsonData, err = json.MarshalIndent(v, "", "  ")
	} else {
		jsonData, err = json.Marshal(v)
	}

	if err != nil {
		log.Printf("Error marshaling struct to JSON: %v", err)
		return ""
	}

	return string(jsonData)
}

// ToJSON 将数据转换为JSON字符串
func ToJSON(v interface{}) string {
	if v == nil {
		return ""
	}

	// 使用 json.MarshalIndent 生成格式化的 JSON
	bytes, err := json.Marshal(v)
	if err != nil {
		return ""
	}

	return string(bytes)
}

// JSONMaskFields 对JSON中的敏感字段进行脱敏
func JSONMaskFields(jsonStr string, sensitiveFields []string) string {
	if jsonStr == "" || len(sensitiveFields) == 0 {
		return jsonStr
	}

	var data map[string]interface{}
	if err := json.Unmarshal([]byte(jsonStr), &data); err != nil {
		return jsonStr
	}

	// 递归处理敏感字段
	maskMap(data, sensitiveFields)

	// 重新序列化
	//result, err := json.MarshalIndent(data, "", "    ")
	result, err := json.Marshal(data)
	if err != nil {
		return jsonStr
	}

	return string(result)
}

// maskMap 递归处理 map 中的敏感字段
func maskMap(data map[string]interface{}, sensitiveFields []string) {
	for k, v := range data {
		switch val := v.(type) {
		case map[string]interface{}:
			maskMap(val, sensitiveFields)
		case []interface{}:
			for _, item := range val {
				if m, ok := item.(map[string]interface{}); ok {
					maskMap(m, sensitiveFields)
				}
			}
		default:
			// 检查是否是敏感字段
			for _, field := range sensitiveFields {
				if strings.EqualFold(k, field) {
					data[k] = "******"
					break
				}
			}
		}
	}
}

func GenerateRandomString32() (string, error) {
	// 创建一个字节切片，用于存储随机字节数据
	bytes := make([]byte, 16) // 16 bytes = 32 hex characters
	_, err := rand.Read(bytes)
	if err != nil {
		return "", err
	}

	// 将字节数据编码为十六进制字符串
	return hex.EncodeToString(bytes), nil
}

// IsValidPhone 验证手机号格式
func IsValidPhone(phone string) bool {
	if phone == "" {
		return false
	}
	// 中国大陆手机号的正则表达式
	phoneRegex := regexp.MustCompile(`^1[3-9]\d{9}$`)
	return phoneRegex.MatchString(phone)
}

func MD5(input string) string {
	// 创建新的MD5哈希对象
	hasher := md5.New()

	// 将输入字符串写入哈希对象
	hasher.Write([]byte(input))

	// 计算哈希并返回十六进制表示的字符串
	return hex.EncodeToString(hasher.Sum(nil))
}

// GenerateUniqueSN 生成唯一流水号(格式:YYYYMMDDHHmmss + 8位随机数)，核销订单号
func GenerateUniqueSN() string {
	ts := time.Now().Format("20060102150405")
	randNum := rand2.New(rand2.NewSource(time.Now().UnixNano())).Intn(90000000) + 10000000
	return fmt.Sprintf("%s%d", ts, randNum)
}
