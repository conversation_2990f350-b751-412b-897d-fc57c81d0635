package admin

import (
	"marketing/internal/dao"
	repository "marketing/internal/dao/reimbursement"
	"marketing/internal/handler/admin/reimbursement"
	db2 "marketing/internal/pkg/db"
	service "marketing/internal/service/reimbursement"

	"github.com/gin-gonic/gin"
)

type ReimbursementRouter struct {
	policyHandler        reimbursement.PolicyHandler
	reimbursementHandler reimbursement.ReimbursementHandler
	balanceHandler       reimbursement.BalanceHandler
	promotionalProducts  reimbursement.PromotionalProducts
	advertExpense        reimbursement.AdvertExpense
}

func NewReimbursementRouter() *ReimbursementRouter {
	// Initialize the database connection (db) here
	db := db2.GetDB()
	balanceRepository := repository.NewBalanceRepository(db)
	reimbursementRepository := repository.NewReimbursementRepository(db)
	policyRepository := repository.NewPolicyRepository(db)
	policyService := service.NewPolicyService(policyRepository)

	reimbursementService := service.NewReimbursementService(reimbursementRepository, policyRepository, balanceRepository)
	productsService := service.NewProductsService(reimbursementRepository, policyRepository, balanceRepository)
	advertService := service.NewAdvertService(reimbursementRepository, policyRepository, balanceRepository)
	policyHandler := reimbursement.NewPolicy(policyService)
	reimbursementHandler := reimbursement.NewReimbursement(policyService, reimbursementService)
	balanceHandler := reimbursement.NewBalanceHandler(service.NewBalanceService(balanceRepository, policyRepository, dao.NewKingDeeAgencyDao()))
	promotionalProducts := reimbursement.NewPromotionalProducts(policyService, productsService)
	advertExpense := reimbursement.NewAdvertExpense(policyService, advertService)
	return &ReimbursementRouter{
		policyHandler:        policyHandler,
		reimbursementHandler: reimbursementHandler,
		balanceHandler:       balanceHandler,
		promotionalProducts:  promotionalProducts,
		advertExpense:        advertExpense,
	}
}

func (rr *ReimbursementRouter) Register(r *gin.RouterGroup) {
	// Policy routes
	r.GET("/policy", rr.policyHandler.GetPolicyList)
	r.POST("/policy", rr.policyHandler.CreatePolicy)
	r.GET("/policy/:id", rr.policyHandler.GetPolicyDetail)
	r.PUT("/policy/:id", rr.policyHandler.UpdatePolicy)
	r.POST("/policy/archive", rr.policyHandler.ArchivePolicy)
	r.GET("/approve-users", rr.policyHandler.GetApproveUserList)

	// 核销申请单汇总(对应reimbursement/apply_summary)
	r.GET("/apply-summary", rr.reimbursementHandler.GetPolicySummary)
	//促销品核销
	r.GET("/promotional-products/quick-pick-stat", rr.promotionalProducts.GetPromotionalProductsQuickPickStat)
	r.GET("/promotional-products/header", rr.promotionalProducts.GetPromotionalProductsHeader)
	r.GET("/promotional-products/list", rr.promotionalProducts.GetPromotionalProductsList)
	r.GET("/promotional-products/detail", rr.promotionalProducts.GetPromotionalProductsDetail)
	r.PUT("/promotional-products/invalid", rr.promotionalProducts.InvalidPromotionalProducts)
	r.PUT("/promotional-products/review-voucher", rr.promotionalProducts.ReviewVoucherPromotionalProducts) //促销品收货单审核
	r.PUT("/promotional-products/review-data", rr.promotionalProducts.ReviewDataPromotionalProducts)       //促销品收货单审核
	r.POST("promotional_products/express/add", rr.promotionalProducts.AddExpressInfo)                      // 添加快递信息
	r.POST("/promotional_products/order-split", rr.promotionalProducts.PromotionalProductsSplit)

	//广告费核销
	r.GET("/advert-expense/quick-pick-stat", rr.advertExpense.GetAdvertExpenseQuickPickStat)
	r.GET("/advert-expense/list", rr.advertExpense.GetAdvertExpenseList)
	r.GET("/advert-expense/detail", rr.advertExpense.GetAdvertExpenseDetail)
	r.PUT("/advert-expense/invalid", rr.advertExpense.InvalidAdvertExpense)
	r.PUT("/advert-expense/audit", rr.advertExpense.AuditAdvertExpense)
	r.POST("/advert-expense/order-split", rr.advertExpense.AdvertExpenseOrderSplit)

	// 核销单
	//对应旧的reimbursement/summary/group_by_policy
	r.GET("/overview-summary", rr.reimbursementHandler.ReimbursementPolicySummary)
	//对应旧的reimbursement/summary
	r.GET("/company-summary", rr.reimbursementHandler.ReimbursementCompanySummary)
	r.PUT("/material-sign-in", rr.reimbursementHandler.MaterialSignIn)
	r.GET("/apply-order-summary/shortcut/stat", rr.reimbursementHandler.GetSummaryShortcutStat)
	r.GET("/apply-order-summary/list", rr.reimbursementHandler.GetSummaryList)
	r.POST("/invalid", rr.reimbursementHandler.ReimbursementInvalid)
	r.POST("/submit", rr.reimbursementHandler.ReimbursementSubmit)
	r.POST("/retrial", rr.reimbursementHandler.ReimbursementRetrial)
	r.POST("/audit", rr.reimbursementHandler.ReimbursementAudit)

	r.GET("/balance/standard", rr.balanceHandler.GetBalanceStandard)
	r.GET("/balance/list", rr.balanceHandler.GetBalanceList)
	r.POST("/balance/import", rr.balanceHandler.ImportBalanceStandard)
	r.POST("/balance/reset", rr.balanceHandler.ResetBalanceStandard)
}
