package agency

import (
	"encoding/json"
	"github.com/gin-gonic/gin"
	"marketing/internal/handler"
	"marketing/internal/model"
	"marketing/internal/pkg/e"
	"marketing/internal/pkg/errors"
	"marketing/internal/service"
)

type TAgency struct {
	svc service.AgencySvcInterface
}

func NewAgency(svc service.AgencySvcInterface) *TAgency {
	return &TAgency{
		svc: svc,
	}
}

func (a *TAgency) AgencyList(c *gin.Context) {
	list := a.svc.GetAgencyList(c)
	handler.Success(c, gin.H{
		"list": list,
	})
}

func (a *TAgency) EditAgency(c *gin.Context) {
	kingDeeIds := make([]int, 0)
	regionIds := make([]int, 0)
	_ = json.Unmarshal([]byte(e.ReqParamStr(c, "kingdee_id")), &kingDeeIds)
	_ = json.Unmarshal([]byte(e.ReqParamStr(c, "region_id")), &regionIds)

	letterName := e.ReqParamStr(c, "letter_name")
	marketCoefficient := e.ReqParamStr(c, "market_coefficient")
	studentsPercent := e.ReqParamStr(c, "students_percent")
	marketType := e.ReqParamStr(c, "market_type")
	flatDate := e.ReqParamStr(c, "flat_date")
	channel := e.ReqParamStr(c, "channel")

	err := a.svc.EditAgency(c, &model.Agency{
		ID:                uint(e.ReqParamInt(c, "id")),
		Name:              e.ReqParamStr(c, "name"),
		PID:               e.ReqParamInt(c, "pid"),
		Partition:         e.ReqParamInt(c, "partition"),
		Department:        uint8(e.ReqParamInt(c, "department")),
		LetterName:        &letterName,
		MarketCoefficient: &marketCoefficient,
		PupilsNum:         uint(e.ReqParamInt(c, "pupils_num")),
		JuniorsNum:        uint(e.ReqParamInt(c, "juniors_num")),
		StudentsNum:       uint(e.ReqParamInt(c, "students_num")),
		StudentsPercent:   &studentsPercent,
		MarketType:        &marketType,
		IsFlat:            uint8(e.ReqParamInt(c, "is_flat")),
		FlatCity:          uint(e.ReqParamInt(c, "flat_city")),
		FlatDate:          &flatDate,
		Channel:           &channel,
	}, kingDeeIds, regionIds)
	if err != nil {
		handler.Error(c, errors.NewErr(err.Error()))
		return
	}

	handler.Success(c, gin.H{})
}

func (a *TAgency) DeleteAgency(c *gin.Context) {
	err := a.svc.DeleteAgency(c, e.ReqParamInt(c, "id"))
	if err != nil {
		handler.Error(c, errors.NewErr(err.Error()))
		return
	}

	handler.Success(c, gin.H{})
}
