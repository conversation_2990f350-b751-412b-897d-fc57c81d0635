package renew

import (
	"github.com/gin-gonic/gin"
	"github.com/spf13/cast"
	"marketing/internal/api/renew"
	"marketing/internal/consts"
	"marketing/internal/handler"
	appError "marketing/internal/pkg/errors"
	service "marketing/internal/service/renew"
)

type ApplicationHandlerInterface interface {
	Add(c *gin.Context)
	GetMachine(c *gin.Context)
	GetEndpoint(c *gin.Context)
	GetIssue(c *gin.Context)
	GetStatus(c *gin.Context)
	Update(c *gin.Context)
	Audit(c *gin.Context)
	Lists(c *gin.Context)
	GetInfo(c *gin.Context)
	Delete(c *gin.Context)
	Export(c *gin.Context)
	GetWarranties(c *gin.Context)
}

type application struct {
	svc service.ApplicationServiceInterface
}

func NewApplicationHandler(svc service.ApplicationServiceInterface) ApplicationHandlerInterface {
	return &application{
		svc: svc,
	}
}

func (h *application) GetMachine(c *gin.Context) {
	barcode := c.Query("barcode")
	if barcode == "" {
		handler.Error(c, appError.NewErr("barcode不能为空"))
		return
	}

	data, err := h.svc.GetMachine(c, barcode)
	if err != nil {
		handler.Error(c, err)
		return
	}
	handler.Success(c, data)
}

func (h *application) GetEndpoint(c *gin.Context) {
	data, err := h.svc.GetEndpoint(c)
	if err != nil {
		handler.Error(c, err)
		return
	}
	handler.Success(c, data)
}

func (h *application) GetIssue(c *gin.Context) {
	data := consts.GetRenewIssues()
	handler.Success(c, data)
}

func (h *application) GetStatus(c *gin.Context) {
	data := consts.GetRenewStatusSlice()
	handler.Success(c, data)
}

func (h *application) Add(c *gin.Context) {
	// 1. 参数校验
	var req renew.AddApplicationReq
	if err := c.ShouldBind(&req); err != nil {
		handler.Error(c, err)
		return
	}
	req.ApplicantType = consts.AgencyPrefix
	err := h.svc.Add(c, &req)
	if err != nil {
		handler.Error(c, err)
		return
	}
	handler.Success(c)
}

func (h *application) Update(c *gin.Context) {
	id := cast.ToUint(c.Param("id"))
	if id == 0 {
		handler.Error(c, appError.NewErr("ID不能为空"))
		return
	}
	req := &renew.AddApplicationReq{}
	if err := c.ShouldBindJSON(req); err != nil {
		handler.Error(c, err)
		return
	}
	req.ID = id
	req.ApplicantType = consts.AgencyPrefix
	err := h.svc.Update(c, req)
	if err != nil {
		handler.Error(c, err)
		return
	}
	handler.Success(c)
}

func (h *application) Audit(c *gin.Context) {
	req := &renew.AuditApplicationReq{}
	if err := c.ShouldBindJSON(req); err != nil {
		handler.Error(c, err)
		return
	}
	id := cast.ToUint(c.Param("id"))
	if id == 0 {
		handler.Error(c, appError.NewErr("ID不能为空"))
		return
	}
	req.ID = id

	req.AuditType = consts.AgencyPrefix

	err := h.svc.Audit(c, req)
	if err != nil {
		handler.Error(c, err)
		return
	}
	handler.Success(c)
}

func (h *application) Lists(c *gin.Context) {
	var req renew.ListApplicationReq
	if err := c.ShouldBind(&req); err != nil {
		handler.Error(c, err)
		return
	}
	req.SetDefaults()
	data, total, err := h.svc.Lists(c, &req)
	if err != nil {
		handler.Error(c, err)
		return
	}
	handler.Success(c, gin.H{
		"list":  data,
		"total": total,
	})
}

func (h *application) GetInfo(c *gin.Context) {
	id := cast.ToUint(c.Param("id"))
	if id == 0 {
		handler.Error(c, appError.NewErr("ID不能为空"))
		return
	}
	data, err := h.svc.GetInfo(c, id)
	if err != nil {
		handler.Error(c, err)
		return
	}
	handler.Success(c, data)
}

func (h *application) Delete(c *gin.Context) {
	id := cast.ToUint(c.Param("id"))
	if id == 0 {
		handler.Error(c, appError.NewErr("ID不能为空"))
		return
	}
	err := h.svc.Delete(c, id, consts.AgencyPrefix)
	if err != nil {
		handler.Error(c, err)
		return
	}
	handler.Success(c)
}

func (h *application) Export(c *gin.Context) {
	var req renew.ListApplicationReq
	if err := c.ShouldBind(&req); err != nil {
		handler.Error(c, err)
		return
	}
	req.SetDefaults()
	err := h.svc.Export(c, &req)
	if err != nil {
		handler.Error(c, err)
		return
	}
}
func (h *application) GetWarranties(c *gin.Context) {
	barcode := c.Query("barcode")
	if barcode == "" {
		handler.Error(c, appError.NewErr("barcode不能为空"))
		return
	}

	data, err := h.svc.GetWarranties(c, barcode)
	if err != nil {
		handler.Error(c, err)
		return
	}
	handler.Success(c, data)
}
