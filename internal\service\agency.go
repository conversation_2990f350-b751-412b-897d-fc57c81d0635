package service

import (
	"errors"
	"fmt"
	"github.com/gin-gonic/gin"
	"marketing/internal/dao"
	"marketing/internal/model"
	"marketing/internal/pkg/utils"
	"sync"
)

type AgencyInfo struct {
	Id             int                       `json:"id"`              // id
	Name           string                    `json:"name"`            // 代理
	Pid            int                       `json:"pid"`             // 父id
	Channel        string                    `json:"channel"`         // 渠道
	ChannelName    string                    `json:"channel_name"`    // 渠道名称
	Department     int                       `json:"department"`      // 部门归属
	DepartmentName string                    `json:"department_name"` // 部门归属名称
	Level          int                       `json:"level"`           // 等级
	Partition      int                       `json:"partition"`       // 大区id
	MarketType     string                    `json:"market_type"`     // 市场类型
	TopPartition   AgencyPartitionInfo       `json:"top_partition"`   // 大区
	Regions        []AgencyRegion            `json:"regions"`         // 关联地区
	KingDeeClients []AgencyKingDeeClientInfo `json:"kingdee_clients"` // 金蝶用户
	Children       []AgencyInfo              `json:"children"`        // 子列表
}

type AgencyPartitionInfo struct {
	Id   int    `json:"id"`
	Name string `json:"name"`
}

type AgencyRegion struct {
	RegionId   int    `json:"region_id"`
	RegionName string `json:"region_name"`
	Pivot      struct {
		AgencyId int `json:"agency_id"`
		RegionId int `json:"region_id"`
	} `json:"pivot"`
}

type AgencyKingDeeClientInfo struct {
	Id        int    `json:"id"`
	ShortName string `json:"short_name"`
	Pivot     struct {
		AgencyId  int `json:"agency_id"`
		KingDeeId int `json:"kingdee_id"`
	} `json:"pivot"`
}

var (
	agencyArray      []AgencyInfo
	agencyArrayMutex sync.RWMutex
	agencyArrayOnce  sync.Once
)

type AgencySvcInterface interface {
	GetAgencyList(c *gin.Context) []AgencyInfo
	RefreshAgencyList(c *gin.Context) []AgencyInfo
	EditAgency(c *gin.Context, agency *model.Agency, kingDeeIds, regionIds []int) error
	DeleteAgency(c *gin.Context, agencyId int) error
}

type AgencySvcImpl struct {
	agencyRepo    dao.AgencyDao
	partitionRepo *dao.PartitionDao
	kingDeeDao    dao.KingDeeAgencyDao
	channelDao    dao.ChannelDao
}

func NewAgencyService(agencyRepo dao.AgencyDao, partitionRepo *dao.PartitionDao, kingDeeDao dao.KingDeeAgencyDao, channelDao dao.ChannelDao) AgencySvcInterface {
	return &AgencySvcImpl{
		agencyRepo:    agencyRepo,
		partitionRepo: partitionRepo,
		kingDeeDao:    kingDeeDao,
		channelDao:    channelDao,
	}
}

func (s *AgencySvcImpl) GetAgencyList(c *gin.Context) []AgencyInfo {
	agencyArrayOnce.Do(func() {
		s.RefreshAgencyList(c)
	})

	agencyArrayMutex.RLock()
	defer agencyArrayMutex.RUnlock()

	return agencyArray
}

func (s *AgencySvcImpl) RefreshAgencyList(c *gin.Context) []AgencyInfo {
	agencyArrayMutex.Lock()
	defer agencyArrayMutex.Unlock()

	agencyArray = make([]AgencyInfo, 0)
	agencyList := s.agencyRepo.GetAgencyList(c)
	channelList := s.channelDao.GetAllChannel(c)
	partitionList := s.partitionRepo.GetAllPartition()
	agencyKingDeeList := s.agencyRepo.GetAgencyKingDeeList(c)
	agencyRegionList := s.agencyRepo.GetAgencyRegionList(c)

	for _, agency := range agencyList {
		info := AgencyInfo{
			Id:             int(agency.ID),
			Name:           agency.Name,
			Pid:            agency.PID,
			Channel:        *agency.Channel,
			Department:     int(agency.Department),
			DepartmentName: s.getDepartmentName(agency.Department),
			Level:          agency.Level,
			Partition:      agency.Partition,
			MarketType:     *agency.MarketType,
			Regions:        make([]AgencyRegion, 0),
			KingDeeClients: make([]AgencyKingDeeClientInfo, 0),
			Children:       make([]AgencyInfo, 0),
		}

		for _, channel := range channelList {
			if *agency.Channel == channel.Code {
				info.ChannelName = channel.Name
				break
			}
		}

		for _, partition := range partitionList {
			if agency.Partition == int(partition.ID) {
				info.TopPartition.Id = int(partition.ID)
				info.TopPartition.Name = partition.Name
				break
			}
		}

		for _, agencyKingDee := range agencyKingDeeList {
			if agency.ID == uint(agencyKingDee.AgencyId) {
				info.KingDeeClients = append(info.KingDeeClients, AgencyKingDeeClientInfo{
					Id:        agencyKingDee.KingDeeId,
					ShortName: agencyKingDee.ShortName,
					Pivot: struct {
						AgencyId  int `json:"agency_id"`
						KingDeeId int `json:"kingdee_id"`
					}{
						AgencyId:  int(agency.ID),
						KingDeeId: agencyKingDee.KingDeeId,
					},
				})
			}
		}

		for _, agencyRegion := range agencyRegionList {
			if agency.ID == uint(agencyRegion.AgencyId) {
				info.Regions = append(info.Regions, AgencyRegion{
					RegionId:   agencyRegion.RegionId,
					RegionName: agencyRegion.RegionName,
					Pivot: struct {
						AgencyId int `json:"agency_id"`
						RegionId int `json:"region_id"`
					}{
						AgencyId: int(agency.ID),
						RegionId: agencyRegion.RegionId,
					},
				})
			}
		}

		parentIds := make([]int, 0)
		if agency.Level > 1 {
			parentIds = s.getAgencyParentIds(agency.PID, &agencyList)
			if len(parentIds) == 0 {
				continue
			}
		}

		s.addAgency(parentIds, &agencyArray, info)
	}

	return agencyArray
}

func (s *AgencySvcImpl) EditAgency(c *gin.Context, agency *model.Agency, kingDeeIds, regionIds []int) error {
	agencyArrayMutex.Lock()
	defer agencyArrayMutex.Unlock()

	// 除了总代,下级代理没有市场分类,无法关联金蝶
	if agency.PID != 0 {
		agency.MarketType = new(string)
		kingDeeIds = make([]int, 0)
	}

	p, _ := s.partitionRepo.GetPartitionByID(agency.Partition)
	if p == nil {
		p = new(dao.Partition)
	}

	hasKingDeeIds := make([]int, 0)
	kingDeeList := s.kingDeeDao.GetKingDeeAgencyList(c, kingDeeIds)
	for _, kingDee := range kingDeeList {
		hasKingDeeIds = append(hasKingDeeIds, kingDee.Id)
	}

	for _, kingDeeId := range kingDeeIds {
		if !utils.IntHas(kingDeeId, hasKingDeeIds) {
			return errors.New("金蝶代理:" + fmt.Sprintf("%d", kingDeeId) + "不存在")
		}
	}

	hasRegionIds := make([]int, 0)
	regionList := dao.GetRegionsByIds(regionIds)
	for _, r := range regionList {
		hasRegionIds = append(hasRegionIds, r.RegionId)
	}

	for _, regionId := range regionIds {
		if !utils.IntHas(regionId, hasRegionIds) {
			return errors.New("区域:" + fmt.Sprintf("%d", regionId) + "不存在")
		}
	}

	if agency.ID == 0 {
		return s.AddAgency(c, agency, p, kingDeeList, regionList)
	}

	oldKingDeeIds := make([]int, 0) // 原始金蝶id
	delKingDeeIds := make([]int, 0) // 解绑金蝶id
	addKingDeeIds := make([]int, 0) // 绑定金蝶id
	oldRegionIds := make([]int, 0)  // 原始区域id
	delRegionIds := make([]int, 0)  // 解绑区域id
	addRegionIds := make([]int, 0)  // 绑定区域id

	oldAgency := s.agencyRepo.GetAgencyById(c, agency.ID)
	if oldAgency == nil {
		return errors.New("编辑代理:代理不存在")
	}

	oldAgencyKingDeeList := s.agencyRepo.GetAgencyKingDeeByAgencyId(c, int(agency.ID))
	for _, k := range oldAgencyKingDeeList {
		oldKingDeeIds = append(oldKingDeeIds, k.KingDeeId)
		if !utils.IntHas(k.KingDeeId, hasKingDeeIds) {
			delKingDeeIds = append(delKingDeeIds, k.KingDeeId)
		}
	}

	oldAgencyRegionList := s.agencyRepo.GetAgencyRegionByAgencyId(c, int(agency.ID))
	for _, r := range oldAgencyRegionList {
		oldRegionIds = append(oldRegionIds, r.RegionId)
		if !utils.IntHas(r.RegionId, hasRegionIds) {
			delRegionIds = append(delRegionIds, r.RegionId)
		}
	}

	for _, hasKingDeeId := range hasKingDeeIds {
		if !utils.IntHas(hasKingDeeId, oldKingDeeIds) {
			addKingDeeIds = append(addKingDeeIds, hasKingDeeId)
		}
	}

	for _, hasRegionId := range hasRegionIds {
		if !utils.IntHas(hasRegionId, oldRegionIds) {
			addRegionIds = append(addRegionIds, hasRegionId)
		}
	}

	uMap := make(map[string]interface{})
	childList := make([]AgencyInfo, 0)

	if oldAgency.PID != agency.PID {
		parentAgency := s.agencyRepo.GetAgencyById(c, uint(agency.PID))
		if parentAgency == nil {
			return errors.New("编辑代理:上级代理不存在")
		}
		uMap["pid"] = parentAgency.ID
		agency.Level = parentAgency.Level + 1
		uMap["level"] = agency.Level
	}

	uMap["name"] = agency.Name
	uMap["partition"] = agency.Partition
	uMap["department"] = agency.Department
	uMap["letter_name"] = agency.LetterName
	uMap["market_coefficient"] = agency.MarketCoefficient
	uMap["pupils_num"] = agency.PupilsNum
	uMap["juniors_num"] = agency.JuniorsNum
	uMap["students_num"] = agency.StudentsNum
	uMap["students_percent"] = agency.StudentsPercent
	uMap["market_type"] = agency.MarketType
	uMap["is_flat"] = agency.IsFlat
	uMap["flat_city"] = agency.FlatCity
	uMap["flat_date"] = agency.FlatDate
	uMap["channel"] = agency.Channel

	dbErr := s.agencyRepo.UpdateAgency(c, agency.ID, uMap, delKingDeeIds, delRegionIds, addKingDeeIds, addRegionIds)
	if dbErr != nil {
		return dbErr
	}

	if oldAgency.PID != agency.PID {
		// 获取父id
		oldsParentIds := s.getAgencyParentIdsFromDb(oldAgency.PID)
		// 获取子节点
		childList = s.getChildAgency(oldsParentIds, &agencyArray, int(agency.ID))
		s.delAgency(oldsParentIds, &agencyArray, int(agency.ID))
	}

	info := AgencyInfo{
		Id:             int(agency.ID),
		Name:           agency.Name,
		Pid:            agency.PID,
		Channel:        *agency.Channel,
		ChannelName:    s.getChannelName(*agency.Channel),
		Department:     int(agency.Department),
		DepartmentName: s.getDepartmentName(agency.Department),
		Level:          agency.Level,
		Partition:      agency.Partition,
		MarketType:     *agency.MarketType,
		TopPartition: AgencyPartitionInfo{
			Id:   int(p.ID),
			Name: p.Name,
		},
		Regions:        make([]AgencyRegion, 0),
		KingDeeClients: make([]AgencyKingDeeClientInfo, 0),
		Children:       childList,
	}

	for _, kingDee := range kingDeeList {
		info.KingDeeClients = append(info.KingDeeClients, AgencyKingDeeClientInfo{
			Id:        kingDee.Id,
			ShortName: kingDee.ShortName,
			Pivot: struct {
				AgencyId  int `json:"agency_id"`
				KingDeeId int `json:"kingdee_id"`
			}{
				AgencyId:  int(agency.ID),
				KingDeeId: kingDee.Id,
			},
		})
	}

	for _, region := range regionList {
		info.Regions = append(info.Regions, AgencyRegion{
			RegionId:   region.RegionId,
			RegionName: region.RegionName,
			Pivot: struct {
				AgencyId int `json:"agency_id"`
				RegionId int `json:"region_id"`
			}{
				AgencyId: int(agency.ID),
				RegionId: region.RegionId,
			},
		})
	}

	s.addAgency(s.getAgencyParentIdsFromDb(agency.PID), &agencyArray, info)

	return nil
}

func (s *AgencySvcImpl) AddAgency(c *gin.Context, agency *model.Agency, p *dao.Partition, kingDeeList []*model.KingDeeAgency, regionList []*dao.Region) error {
	// 默认一级代理
	agency.Level = 1

	// 遍历所有金蝶id
	kingDeeIds := make([]int, 0)
	for _, kingDee := range kingDeeList {
		kingDeeIds = append(kingDeeIds, kingDee.Id)
	}

	// 遍历所有区域id
	regionIds := make([]int, 0)
	for _, region := range regionList {
		regionIds = append(regionIds, region.RegionId)
	}

	if agency.PID != 0 {
		parentAgency := s.agencyRepo.GetAgencyById(c, uint(agency.PID))
		if parentAgency == nil {
			return errors.New("创建代理:上级代理不存在")
		}
		agency.Level = parentAgency.Level + 1
		agency.MarketType = new(string)
		kingDeeIds = make([]int, 0)
	}

	dbErr := s.agencyRepo.CreateAgency(c, agency, kingDeeIds, regionIds)
	if dbErr != nil {
		return dbErr
	}

	// 这里暂时不写入代理关联地区的历史记录,旧的服务会自动写入,待确认

	info := AgencyInfo{
		Id:             int(agency.ID),
		Name:           agency.Name,
		Pid:            agency.PID,
		Channel:        *agency.Channel,
		ChannelName:    s.getChannelName(*agency.Channel),
		Department:     int(agency.Department),
		DepartmentName: s.getDepartmentName(agency.Department),
		Level:          agency.Level,
		Partition:      agency.Partition,
		MarketType:     *agency.MarketType,
		TopPartition: AgencyPartitionInfo{
			Id:   int(p.ID),
			Name: p.Name,
		},
		KingDeeClients: make([]AgencyKingDeeClientInfo, 0),
		Children:       make([]AgencyInfo, 0),
	}

	for _, kingDee := range kingDeeList {
		info.KingDeeClients = append(info.KingDeeClients, AgencyKingDeeClientInfo{
			Id:        kingDee.Id,
			ShortName: kingDee.ShortName,
			Pivot: struct {
				AgencyId  int `json:"agency_id"`
				KingDeeId int `json:"kingdee_id"`
			}{
				AgencyId:  int(agency.ID),
				KingDeeId: kingDee.Id,
			},
		})
	}

	for _, region := range regionList {
		info.Regions = append(info.Regions, AgencyRegion{
			RegionId:   region.RegionId,
			RegionName: region.RegionName,
			Pivot: struct {
				AgencyId int `json:"agency_id"`
				RegionId int `json:"region_id"`
			}{
				AgencyId: int(agency.ID),
				RegionId: region.RegionId,
			},
		})
	}

	s.addAgency(s.getAgencyParentIdsFromDb(agency.PID), &agencyArray, info)

	return nil
}

func (s *AgencySvcImpl) DeleteAgency(c *gin.Context, agencyId int) error {
	agencyArrayMutex.Lock()
	defer agencyArrayMutex.Unlock()

	agency := s.agencyRepo.GetAgencyById(c, uint(agencyId))
	if agency == nil {
		return errors.New("删除代理:代理不存在")
	}

	dbErr := s.agencyRepo.DeleteAgency(c, uint(agencyId))
	if dbErr != nil {
		return dbErr
	}

	// TODO 还需要解绑终端和代理的关联

	s.delAgency(s.getAgencyParentIdsFromDb(agency.PID), &agencyArray, int(agency.ID))

	return nil
}

func (s *AgencySvcImpl) addAgency(parentIds []int, agencyArray *[]AgencyInfo, agency AgencyInfo) {
	if len(parentIds) > 0 {
		for i, c := range *agencyArray {
			if c.Id == parentIds[0] {
				s.addAgency(parentIds[1:], &(*agencyArray)[i].Children, agency)
				return
			}
		}

		parentAgency := AgencyInfo{
			Id:       parentIds[0],
			Children: make([]AgencyInfo, 0),
		}

		// 加入子标签
		s.addAgency(parentIds[1:], &parentAgency.Children, agency)
		// 加入父标签
		*agencyArray = append(*agencyArray, parentAgency)

		return
	}

	for i, a := range *agencyArray {
		if a.Id == agency.Id {
			(*agencyArray)[i] = agency
			return
		}
	}

	*agencyArray = append(*agencyArray, agency)

	return
}

func (s *AgencySvcImpl) delAgency(parentIds []int, agencyArray *[]AgencyInfo, id int) {
	if len(parentIds) > 0 {
		for i, c := range *agencyArray {
			if c.Id == parentIds[0] {
				s.delAgency(parentIds[1:], &(*agencyArray)[i].Children, id)
			}
		}
	}

	childList := make([]AgencyInfo, 0)
	for _, c := range *agencyArray {
		if c.Id != id {
			childList = append(childList, c)
		}
	}

	*agencyArray = childList

	return
}

func (s *AgencySvcImpl) getChildAgency(parentIds []int, categoryArray *[]AgencyInfo, id int) []AgencyInfo {
	if len(parentIds) > 0 {
		for i, c := range *categoryArray {
			if c.Id == parentIds[0] {
				return s.getChildAgency(parentIds[1:], &(*categoryArray)[i].Children, id)
			}
		}
	}

	for _, c := range *categoryArray {
		if c.Id == id {
			return c.Children
		}
	}

	return make([]AgencyInfo, 0)
}

func (s *AgencySvcImpl) getAgencyParentIds(pid int, agencyList *[]*model.Agency) []int {
	parentIds := make([]int, 0)

	for _, agency := range *agencyList {
		if agency.ID == uint(pid) {
			if agency.Level > 1 {
				parentIds = append(parentIds, s.getAgencyParentIds(agency.PID, agencyList)...)
				// 判断parentIds是否为空,为空说明父标签已被删除
				if len(parentIds) == 0 {
					return make([]int, 0)
				}
			}
			parentIds = append(parentIds, int(agency.ID))
		}
	}

	return parentIds
}

func (s *AgencySvcImpl) getAgencyParentIdsFromDb(id int) []int {
	parentIds := make([]int, 0)

	category := s.agencyRepo.GetAgencyById(new(gin.Context), uint(id))
	if category == nil {
		return parentIds
	}

	if category.Level > 1 {
		parentIds = append(parentIds, s.getAgencyParentIdsFromDb(category.PID)...)
	}

	return append(parentIds, int(category.ID))
}

func (s *AgencySvcImpl) getDepartmentName(department uint8) string {
	switch department {
	case 1:
		return "渠道部华南大区"
	case 2:
		return "渠道部华东大区"
	case 3:
		return "渠道部华北大区"
	case 4:
		return "渠道部西南大区"
	}
	return "未知"
}

func (s *AgencySvcImpl) getChannelName(channel string) string {
	c := s.channelDao.GetChannelByCode(new(gin.Context), channel)
	if c == nil {
		return ""
	}
	return c.Name
}
