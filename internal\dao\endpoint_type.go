package dao

import (
	"gorm.io/gorm"
	"marketing/internal/model"
	"time"
)

// EndpointTypeDao 结构体用于封装与数据库的交互
type EndpointTypeDao struct {
	DB *gorm.DB
}

// NewEndpointTypeDao 创建一个新的 EndpointTypeDao
func NewEndpointTypeDao(db *gorm.DB) *EndpointTypeDao {
	return &EndpointTypeDao{DB: db}
}

// GetAll 获取所有记录
func (dao *EndpointTypeDao) GetAll() ([]model.EndpointType, error) {
	var endpointTypes []model.EndpointType
	if err := dao.DB.Where("status = ?", 1).Find(&endpointTypes).Error; err != nil {
		return nil, err
	}
	return endpointTypes, nil
}

// GetByID 根据 ID 获取单个记录
func (dao *EndpointTypeDao) GetByID(id int) (*model.EndpointType, error) {
	var endpointType model.EndpointType
	if err := dao.DB.Where("id = ?", id).First(&endpointType).Error; err != nil {
		return nil, err
	}
	return &endpointType, nil
}

// Create 创建新的记录
func (dao *EndpointTypeDao) Create(endpointType *model.EndpointType) error {
	return dao.DB.Create(endpointType).Error
}

// Update 更新记录（不允许修改 status 字段）
func (dao *EndpointTypeDao) Update(id int, updatedEndpointType *model.EndpointType) (*model.EndpointType, error) {
	var endpointType model.EndpointType
	if err := dao.DB.Where("id = ? AND status = ?", id, 1).First(&endpointType).Error; err != nil {
		return nil, err
	}

	// 只更新除 status 以外的字段
	endpointType.Code = updatedEndpointType.Code
	endpointType.Name = updatedEndpointType.Name
	endpointType.Description = updatedEndpointType.Description
	endpointType.UpdatedAt = time.Now()

	if err := dao.DB.Save(&endpointType).Error; err != nil {
		return nil, err
	}

	return &endpointType, nil
}

// Delete 将 status 设置为 0，表示删除
func (dao *EndpointTypeDao) Delete(id int) error {
	var endpointType model.EndpointType
	if err := dao.DB.Where("id = ?", id).First(&endpointType).Error; err != nil {
		return err
	}
	endpointType.Status = 0
	endpointType.UpdatedAt = time.Now()
	if err := dao.DB.Save(&endpointType).Error; err != nil {
		return err
	}

	return nil
}

// Restore 将 status 设置为 1，表示恢复
func (dao *EndpointTypeDao) Restore(id int) error {
	var endpointType model.EndpointType
	if err := dao.DB.Where("id = ?", id).First(&endpointType).Error; err != nil {
		return err
	}

	endpointType.Status = 1
	endpointType.UpdatedAt = time.Now()
	if err := dao.DB.Save(&endpointType).Error; err != nil {
		return err
	}

	return nil
}
