package reimbursement

import (
	"github.com/spf13/cast"
	api "marketing/internal/api/reimbursement"
	"marketing/internal/dao/admin_user"
	dao "marketing/internal/dao/reimbursement"
	"marketing/internal/handler"
	"marketing/internal/pkg/db"
	"marketing/internal/pkg/errors"
	service "marketing/internal/service/reimbursement"

	"github.com/gin-gonic/gin"
)

// ReimbursementHandler handles reimbursement related requests for agency
type ReimbursementHandler interface {
	// GetHomePage 获取报销首页数据
	GetHomePage(c *gin.Context)
	// GetClientSummary 获取客户端汇总数据
	GetClientSummary(c *gin.Context)
	// GetClientOrders 获取客户端订单数据
	GetClientOrders(c *gin.Context)
	// GetPromotionalProductsDetail 获取促销产品详情
	GetPromotionalProductsDetail(c *gin.Context)
	// GetAdvertExpenseDetail returns advert expense detail
	GetAdvertExpenseDetail(c *gin.Context)
	// GetClientDetail 获取客户端详情
	GetClientDetail(c *gin.Context)
	// GetPolicyDetail 获取政策详情
	GetPolicyDetail(c *gin.Context)
	//ApplyAdvertExpense 广告费申请
	ApplyAdvertExpense(c *gin.Context)
}

type reimbursement struct {
	homePageService service.HomePageService
	productsService service.ProductsService
	advertService   service.AdvertService
	policyService   service.PolicyService
}

// NewReimbursement creates a new ReimbursementHandler for agency
func NewReimbursement() ReimbursementHandler {
	Db := db.GetDB()
	reimbursementRepo := dao.NewReimbursementRepository(Db)
	userRepo := admin_user.NewUserDao(Db)
	balanceRepo := dao.NewBalanceRepository(Db)
	policyRepo := dao.NewPolicyRepository(Db)
	homePageService := service.NewHomePageService(reimbursementRepo, userRepo, balanceRepo, policyRepo)
	productsService := service.NewProductsService(reimbursementRepo, policyRepo, balanceRepo)
	advertService := service.NewAdvertService(reimbursementRepo, policyRepo, balanceRepo)
	policyService := service.NewPolicyService(policyRepo)

	return &reimbursement{
		homePageService: homePageService,
		productsService: productsService,
		advertService:   advertService,
		policyService:   policyService,
	}
}

// GetHomePage 获取报销首页数据
func (r *reimbursement) GetHomePage(c *gin.Context) {
	var req api.HomePageReq
	if err := c.ShouldBind(&req); err != nil {
		handler.Error(c, errors.NewErr("参数错误: "+err.Error()))
		return
	}

	// 调用服务获取首页数据
	resp, err := r.homePageService.GetHomePage(c, &req)
	if err != nil {
		handler.Error(c, err)
		return
	}

	handler.Success(c, resp)
}

// GetClientSummary 获取客户端汇总数据
func (r *reimbursement) GetClientSummary(c *gin.Context) {
	var req api.ClientSummaryReq
	if err := c.ShouldBind(&req); err != nil {
		handler.Error(c, errors.NewErr("参数错误: "+err.Error()))
		return
	}

	// 调用服务获取汇总数据
	resp, err := r.homePageService.GetClientSummary(c, &req)
	if err != nil {
		handler.Error(c, err)
		return
	}

	handler.Success(c, resp)
}

// GetClientOrders 获取客户端订单数据
func (r *reimbursement) GetClientOrders(c *gin.Context) {
	var req api.ClientOrdersReq
	if err := c.ShouldBind(&req); err != nil {
		handler.Error(c, errors.NewErr("参数错误: "+err.Error()))
		return
	}
	req.SetDefaults()
	// 调用服务获取订单数据
	resp, err := r.homePageService.GetClientOrders(c, &req)
	if err != nil {
		handler.Error(c, err)
		return
	}
	handler.Success(c, resp)
}

// GetPromotionalProductsDetail 获取推广产品详情
func (r *reimbursement) GetPromotionalProductsDetail(c *gin.Context) {
	var req api.OrderReq

	// 绑定查询参数
	if err := c.ShouldBindQuery(&req); err != nil {
		handler.Error(c, errors.NewErr("参数错误: "+err.Error()))
		return
	}

	// 调用服务获取详情数据
	detail, err := r.productsService.GetPromotionalProductsDetail(c, &req)
	if err != nil {
		handler.Error(c, err)
		return
	}

	if detail == nil {
		handler.Error(c, errors.NewErr("未找到数据"))
		return
	}

	handler.Success(c, detail)
}

// GetAdvertExpenseDetail 获取广告费用详情
func (r *reimbursement) GetAdvertExpenseDetail(c *gin.Context) {
	var req api.OrderReq

	// 绑定查询参数
	if err := c.ShouldBindQuery(&req); err != nil {
		handler.Error(c, errors.NewErr("参数错误: "+err.Error()))
		return
	}

	// 调用服务获取详情数据
	detail, err := r.advertService.GetAdvertExpenseDetail(c, &req)
	if err != nil {
		handler.Error(c, err)
		return
	}

	if detail == nil {
		handler.Error(c, errors.NewErr("未找到数据"))
		return
	}

	handler.Success(c, detail)
}

// GetClientDetail 获取客户端详情
func (r *reimbursement) GetClientDetail(c *gin.Context) {
	var req api.OrderReq
	if err := c.ShouldBindQuery(&req); err != nil {
		handler.Error(c, errors.NewErr("参数错误: "+err.Error()))
		return
	}
	// 调用服务获取客户端详情
	detail, err := r.homePageService.GetClientDetail(c, &req)
	if err != nil {
		handler.Error(c, err)
		return
	}
	handler.Success(c, detail)
}

// GetPolicyDetail 获取政策详情
func (r *reimbursement) GetPolicyDetail(c *gin.Context) {
	id := c.Param("id")
	if id == "" {
		handler.Error(c, errors.NewErr("缺少政策ID参数"))
		return
	}
	// Call service to get policy detail
	data, err := r.policyService.GetPolicyDetail(cast.ToInt(id))
	if err != nil {
		handler.Error(c, err)
		return
	}
	handler.Success(c, data)
}

// ApplyAdvertExpense 广告费申请
func (r *reimbursement) ApplyAdvertExpense(c *gin.Context) {
	var req api.AdvertExpenseApplyReq
	if err := c.ShouldBind(&req); err != nil {
		handler.Error(c, errors.NewErr("参数错误: "+err.Error()))
		return
	}

	// 调用服务进行广告费申请
	err := r.homePageService.ApplyAdvertExpense(c, &req)
	if err != nil {
		handler.Error(c, err)
		return
	}

	handler.Success(c)
}
