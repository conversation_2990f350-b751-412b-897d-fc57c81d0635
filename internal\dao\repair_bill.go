package dao

import (
	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
	"marketing/internal/model"
	"marketing/internal/pkg/db"
	"marketing/internal/pkg/utils"
)

type GetRepairBillListParam struct {
	AgencyId      int    // 代理id
	BillNumber    string // 工单编号
	SnCode        string // sn码
	CustomerName  string // 客户名称
	CustomerPhone string // 客户手机号
	PageNum       int    // 页码
	PageSize      int    // 页幅
}

// RepairBillMachineAccessoryParam 编辑维修工作单设备配件的参数
type RepairBillMachineAccessoryParam struct {
	Id     int `json:"id" gorm:"-"`     // 配件关联id
	Amount int `json:"amount" gorm:"-"` // 数量
}

type RepairBillDao interface {
	DeleteRepairBill(c *gin.Context, id int) error
	EditRepairBill(c *gin.Context, id int, uMap map[string]interface{}, malfunctionList []*model.MachineMalfunction, accessoryList []*RepairBillMachineAccessoryParam) error
	GetRepairBillList(c *gin.Context, param GetRepairBillListParam) ([]*model.RepairBillDetails, int64)
	GetRepairBillById(c *gin.Context, id int) *model.RepairBillDetails
	GetMachineMalfunctionRelationList(c *gin.Context, repairBillIds []int) (list []*model.RepairBillMachineMalfunctionRelation)
	GetMachineAccessoryList(c *gin.Context, repairBillIds []int) (list []*model.RepairBillMachineAccessoryInfo)
}

// RepairBillDaoImpl 实现 RepairBillDao 接口
type RepairBillDaoImpl struct {
	db *gorm.DB
}

// NewRepairBillDao 创建 RepairBillDao 实例
func NewRepairBillDao() RepairBillDao {
	return &RepairBillDaoImpl{
		db: db.GetDB(""),
	}
}

func (d *RepairBillDaoImpl) DeleteRepairBill(c *gin.Context, id int) error {
	tx := d.db.WithContext(c).Begin()

	if dbErr := tx.Delete(&model.RepairBill{}, "id = ?", id).Error; dbErr != nil {
		tx.Rollback()
		return dbErr
	}

	if dbErr := tx.Delete(&model.RepairBillMachineMalfunctionRelation{}, "repair_bill_id = ?", id).Error; dbErr != nil {
		tx.Rollback()
		return dbErr
	}

	if dbErr := tx.Delete(&model.RepairBillMachineAccessoryRelation{}, "repair_bill_id = ?", id).Error; dbErr != nil {
		tx.Rollback()
		return dbErr
	}

	if err := tx.Commit().Error; err != nil {
		tx.Rollback()
		return err
	}

	return nil
}

func (d *RepairBillDaoImpl) EditRepairBill(c *gin.Context, id int, uMap map[string]interface{}, malfunctionList []*model.MachineMalfunction, accessoryList []*RepairBillMachineAccessoryParam) error {
	tx := d.db.WithContext(c).Begin()

	if dbErr := tx.Model(&model.RepairBill{}).Where("id = ?", id).Updates(uMap).Error; dbErr != nil {
		tx.Rollback()
		return dbErr
	}

	if dbErr := tx.Delete(&model.RepairBillMachineMalfunctionRelation{}, "repair_bill_id = ?", id).Error; dbErr != nil {
		tx.Rollback()
		return dbErr
	}

	if dbErr := tx.Delete(&model.RepairBillMachineAccessoryRelation{}, "repair_bill_id = ?", id).Error; dbErr != nil {
		tx.Rollback()
		return dbErr
	}

	for _, m := range malfunctionList {
		r := &model.RepairBillMachineMalfunctionRelation{
			RepairBillId:   id,
			MalfunctionTop: m.ParentId,
			MalfunctionId:  m.Id,
		}
		if dbErr := tx.Create(r).Error; dbErr != nil {
			tx.Rollback()
			return dbErr
		}
	}

	for _, a := range accessoryList {
		r := &model.RepairBillMachineAccessoryRelation{
			RepairBillId:               id,
			MachineAccessoryRelationId: a.Id,
			Amount:                     a.Amount,
		}
		if dbErr := tx.Create(r).Error; dbErr != nil {
			tx.Rollback()
			return dbErr
		}
	}

	if err := tx.Commit().Error; err != nil {
		tx.Rollback()
		return err
	}

	return nil
}

func (d *RepairBillDaoImpl) GetRepairBillList(c *gin.Context, param GetRepairBillListParam) ([]*model.RepairBillDetails, int64) {
	query := d.db.WithContext(c).Model(&model.RepairBill{})

	if param.AgencyId > 0 {
		query = query.Where("create_uid in (select user_id from user_after_sales where endpoint_id in "+
			"(select endpoint_id from after_sales_endpoint where top_agency_id = ?))", param.AgencyId)
	}

	if len(param.BillNumber) > 0 {
		query = query.Where("bill_number = ?", param.BillNumber)
	}

	if len(param.SnCode) > 0 {
		query = query.Where("sn_code = ?", param.SnCode)
	}

	if len(param.CustomerName) > 0 {
		query = query.Where("customer_name = ?", param.CustomerName)
	}

	if len(param.CustomerPhone) > 0 {
		query = query.Where("customer_phone = ?", param.CustomerPhone)
	}

	query = query.Where("deleted_at is null")

	if param.PageNum < 1 {
		param.PageNum = 1
	}

	if param.PageSize < 1 {
		param.PageSize = 20
	}

	var (
		total int64
		list  []*model.RepairBillDetails
	)

	query.Count(&total)
	query.Offset((param.PageNum - 1) * param.PageSize).Limit(param.PageSize).Order("id desc").Find(&list)

	for _, l := range list {
		l.BuyDateStr = utils.GetTimeStrDay(l.BuyDate)
		l.ReceiveMachineDateStr = utils.GetTimeStrDay(l.ReceiveMachineDate)
		l.ExpressDateStr = utils.GetTimeStrDay(l.ExpressDate)
		l.CreateTime = utils.GetTimeStr(l.CreatedAt)
		l.UpdateTime = utils.GetTimeStr(l.UpdatedAt)
	}

	return list, total
}

func (d *RepairBillDaoImpl) GetRepairBillById(c *gin.Context, id int) *model.RepairBillDetails {
	var repairBill model.RepairBillDetails
	err := d.db.WithContext(c).Model(&model.RepairBill{}).Where("id = ? and deleted_at is null", id).First(&repairBill).Error
	if err != nil {
		return nil
	}

	repairBill.BuyDateStr = utils.GetTimeStrDay(repairBill.BuyDate)
	repairBill.ReceiveMachineDateStr = utils.GetTimeStrDay(repairBill.ReceiveMachineDate)
	repairBill.ExpressDateStr = utils.GetTimeStrDay(repairBill.ExpressDate)
	repairBill.CreateTime = utils.GetTimeStr(repairBill.CreatedAt)
	repairBill.UpdateTime = utils.GetTimeStr(repairBill.UpdatedAt)

	return &repairBill
}

func (d *RepairBillDaoImpl) GetMachineMalfunctionRelationList(c *gin.Context, repairBillIds []int) (list []*model.RepairBillMachineMalfunctionRelation) {
	d.db.WithContext(c).Model(&model.RepairBillMachineMalfunctionRelation{}).Where("repair_bill_id in (?)", repairBillIds).Find(&list)
	return
}

func (d *RepairBillDaoImpl) GetMachineAccessoryList(c *gin.Context, repairBillIds []int) (list []*model.RepairBillMachineAccessoryInfo) {
	d.db.WithContext(c).
		Select("repair_bill_id,accessory_id,title,amount,price").
		Model(&model.RepairBillMachineAccessoryRelation{}).
		Joins("join machine_accessory_relation on repair_bill_machine_accessory_relation.machine_accessory_relation_id = machine_accessory_relation.id").
		Joins("join machine_accessory ON machine_accessory_relation.accessory_id = machine_accessory.id").
		Where("repair_bill_id in (?)", repairBillIds).Find(&list)
	return
}
