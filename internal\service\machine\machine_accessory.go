package machine

import (
	"errors"
	"github.com/gin-gonic/gin"
	"marketing/internal/dao"
	"marketing/internal/model"
	"sync"
)

type TMachineAccessoryInfo struct {
	Id          int                     `json:"id" gorm:"id"`                   // id
	Title       string                  `json:"title" gorm:"title"`             // 标题
	Description string                  `json:"description" gorm:"description"` // 描述
	ChildList   []TMachineAccessoryInfo `json:"child_list"`                     // 子标签列表
}

var (
	machineAccessoryArray      []TMachineAccessoryInfo
	machineAccessoryArrayMutex sync.RWMutex
)

type TMachineAccessorySvcInterface interface {
	GetAllMachineAccessory(c *gin.Context) (infos []TMachineAccessoryInfo)
	RefreshMachineAccessory(c *gin.Context) (infos []TMachineAccessoryInfo)
	EditMachineAccessory(c *gin.Context, id, pId int, title, description string) error
	AddMachineAccessory(c *gin.Context, pId int, title, description string) error
	DelMachineAccessory(c *gin.Context, id int) error
}

type TMachineAccessorySvcImpl struct {
	machineAccessoryRepo dao.MachineAccessoryDao
}

func NewMachineAccessoryService(machineAccessoryRepo dao.MachineAccessoryDao) TMachineAccessorySvcInterface {
	svc := &TMachineAccessorySvcImpl{
		machineAccessoryRepo: machineAccessoryRepo,
	}

	svc.RefreshMachineAccessory(new(gin.Context))

	return svc
}

func (s *TMachineAccessorySvcImpl) GetAllMachineAccessory(c *gin.Context) (infos []TMachineAccessoryInfo) {
	machineAccessoryArrayMutex.RLock()
	defer machineAccessoryArrayMutex.RUnlock()
	return machineAccessoryArray
}

func (s *TMachineAccessorySvcImpl) RefreshMachineAccessory(c *gin.Context) (infos []TMachineAccessoryInfo) {
	machineAccessoryArrayMutex.Lock()
	defer machineAccessoryArrayMutex.Unlock()

	machineAccessoryArray = make([]TMachineAccessoryInfo, 0)
	accessoryList := s.machineAccessoryRepo.GetAllMachineAccessory(c)
	for _, accessory := range accessoryList {
		parentIds := make([]int, 0)
		if accessory.ParentId != 0 {
			parentIds = s.getMachineAccessoryParentIds(accessory.ParentId, accessoryList)
			if len(parentIds) == 0 {
				continue
			}
		}
		s.addAccessory(parentIds, &machineAccessoryArray, TMachineAccessoryInfo{
			Id:          accessory.Id,
			Title:       accessory.Title,
			Description: accessory.Description,
			ChildList:   make([]TMachineAccessoryInfo, 0),
		})
	}

	return machineAccessoryArray
}

func (s *TMachineAccessorySvcImpl) EditMachineAccessory(c *gin.Context, id, pId int, title, description string) error {
	machineAccessoryArrayMutex.Lock()
	defer machineAccessoryArrayMutex.Unlock()

	if len(title) == 0 {
		return errors.New("编辑标签:名称为空")
	}

	accessory := s.machineAccessoryRepo.GetMachineAccessoryById(c, id)
	if accessory == nil {
		return errors.New("编辑标签:标签不存在")
	}

	oldPid := 0
	childList := make([]TMachineAccessoryInfo, 0)
	uMap := make(map[string]interface{})

	if pId == accessory.ParentId {
		accessory.Title = title
		accessory.Description = description
		uMap["title"] = accessory.Title
		uMap["description"] = accessory.Description
	} else {
		parentAccessory := s.machineAccessoryRepo.GetMachineAccessoryById(c, pId)
		if parentAccessory == nil {
			return errors.New("编辑标签:父标签不存在")
		}

		accessory.Title = title
		accessory.Description = description
		oldPid = accessory.ParentId
		accessory.ParentId = parentAccessory.Id
		uMap["title"] = accessory.Title
		uMap["description"] = accessory.Description
		uMap["parent_id"] = accessory.ParentId
	}

	// 修改db
	dbErr := s.machineAccessoryRepo.UpdateMachineAccessory(c, accessory.Id, uMap)
	if dbErr != nil {
		return nil
	}

	if oldPid != accessory.ParentId {
		// 获取父id
		oldsParentIds := s.getMachineAccessoryParentIdsFromDb(oldPid)
		// 获取子节点
		childList = s.getChildAccessory(oldsParentIds, &machineAccessoryArray, accessory.Id)
		s.delAccessory(oldsParentIds, &machineAccessoryArray, accessory.Id)
	}

	s.addAccessory(s.getMachineAccessoryParentIdsFromDb(accessory.ParentId), &machineAccessoryArray, TMachineAccessoryInfo{
		Id:          accessory.Id,
		Title:       accessory.Title,
		Description: accessory.Description,
		ChildList:   childList,
	})

	return nil
}

func (s *TMachineAccessorySvcImpl) AddMachineAccessory(c *gin.Context, pId int, title, description string) error {
	machineAccessoryArrayMutex.Lock()
	defer machineAccessoryArrayMutex.Unlock()

	if len(title) == 0 {
		return errors.New("添加标签:名称为空")
	}

	accessory := &model.MachineAccessory{
		Title:       title,
		Description: description,
		ParentId:    pId,
	}

	if pId != 0 && s.machineAccessoryRepo.GetMachineAccessoryById(c, pId) == nil {
		return errors.New("添加标签:父标签不存在")
	}

	dbErr := s.machineAccessoryRepo.CreateMachineAccessory(c, accessory)
	if dbErr != nil {
		return dbErr
	}

	s.addAccessory(s.getMachineAccessoryParentIdsFromDb(accessory.ParentId), &machineAccessoryArray, TMachineAccessoryInfo{
		Id:          accessory.Id,
		Title:       accessory.Title,
		Description: accessory.Description,
		ChildList:   make([]TMachineAccessoryInfo, 0),
	})

	return nil
}

func (s *TMachineAccessorySvcImpl) DelMachineAccessory(c *gin.Context, id int) error {
	machineAccessoryArrayMutex.Lock()
	defer machineAccessoryArrayMutex.Unlock()

	accessory := s.machineAccessoryRepo.GetMachineAccessoryById(c, id)
	if accessory == nil {
		return errors.New("删除标签:标签不存在")
	}

	dbErr := s.machineAccessoryRepo.DeleteMachineAccessory(c, id)
	if dbErr != nil {
		return dbErr
	}

	s.delAccessory(s.getMachineAccessoryParentIdsFromDb(accessory.ParentId), &machineAccessoryArray, accessory.Id)

	return nil
}

func (s *TMachineAccessorySvcImpl) addAccessory(parentIds []int, accessoryArray *[]TMachineAccessoryInfo, accessory TMachineAccessoryInfo) {
	if len(parentIds) > 0 {
		for i, c := range *accessoryArray {
			if c.Id == parentIds[0] {
				s.addAccessory(parentIds[1:], &(*accessoryArray)[i].ChildList, accessory)
				return
			}
		}

		parentAccessory := TMachineAccessoryInfo{
			Id:        parentIds[0],
			ChildList: make([]TMachineAccessoryInfo, 0),
		}

		// 加入子标签
		s.addAccessory(parentIds[1:], &parentAccessory.ChildList, accessory)
		// 加入父标签
		*accessoryArray = append(*accessoryArray, parentAccessory)

		return
	}

	for i, c := range *accessoryArray {
		if c.Id == accessory.Id {
			(*accessoryArray)[i].Title = accessory.Title
			(*accessoryArray)[i].Description = accessory.Description
			return
		}
	}

	*accessoryArray = append(*accessoryArray, accessory)

	return
}

func (s *TMachineAccessorySvcImpl) delAccessory(parentIds []int, accessoryArray *[]TMachineAccessoryInfo, id int) {
	if len(parentIds) > 0 {
		for i, c := range *accessoryArray {
			if c.Id == parentIds[0] {
				s.delAccessory(parentIds[1:], &(*accessoryArray)[i].ChildList, id)
			}
		}
	}

	childList := make([]TMachineAccessoryInfo, 0)
	for _, a := range *accessoryArray {
		if a.Id != id {
			childList = append(childList, a)
		}
	}

	*accessoryArray = childList

	return
}

func (s *TMachineAccessorySvcImpl) getChildAccessory(parentIds []int, accessoryArray *[]TMachineAccessoryInfo, id int) []TMachineAccessoryInfo {
	if len(parentIds) > 0 {
		for i, c := range *accessoryArray {
			if c.Id == parentIds[0] {
				return s.getChildAccessory(parentIds[1:], &(*accessoryArray)[i].ChildList, id)
			}
		}
	}

	for _, c := range *accessoryArray {
		if c.Id == id {
			return c.ChildList
		}
	}

	return make([]TMachineAccessoryInfo, 0)
}

func (s *TMachineAccessorySvcImpl) getMachineAccessoryParentIds(pid int, accessoryList []*model.MachineAccessory) []int {
	parentIds := make([]int, 0)

	for _, accessory := range accessoryList {
		if accessory.Id == pid {
			if accessory.ParentId != 0 {
				parentIds = append(parentIds, s.getMachineAccessoryParentIds(accessory.ParentId, accessoryList)...)
				// 判断parentIds是否为空,为空说明父标签已被删除
				if len(parentIds) == 0 {
					return make([]int, 0)
				}
			}
			parentIds = append(parentIds, accessory.Id)
		}
	}

	return parentIds
}

func (s *TMachineAccessorySvcImpl) getMachineAccessoryParentIdsFromDb(id int) []int {
	parentIds := make([]int, 0)

	accessory := s.machineAccessoryRepo.GetMachineAccessoryById(new(gin.Context), id)
	if accessory == nil {
		return parentIds
	}

	if accessory.ParentId != 0 {
		parentIds = append(parentIds, s.getMachineAccessoryParentIdsFromDb(accessory.ParentId)...)
	}

	return append(parentIds, accessory.Id)
}
