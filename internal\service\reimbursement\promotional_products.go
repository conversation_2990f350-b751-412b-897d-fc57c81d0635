package reimbursement

import (
	"fmt"
	"github.com/gin-gonic/gin"
	"log"
	api "marketing/internal/api/reimbursement"
	dao "marketing/internal/dao/reimbursement"
	"marketing/internal/pkg/errors"
	"math"
	"os"
)

type ProductsService interface {
	// GetPromotionalProductsQuickPickStat 获取促销产品快捷键统计
	GetPromotionalProductsQuickPickStat(c *gin.Context, req *api.PromotionalProductsQuickPickStatReq) (*api.PromotionalProductsQuickPickStatResp, error)
	// GetPromotionalProductsDetail 获取推广产品详情
	GetPromotionalProductsDetail(c *gin.Context, req *api.OrderReq) (*api.PromotionalProductsDetailResp, error)
	// InvalidPromotionalProducts 促销品申请单作废
	InvalidPromotionalProducts(c *gin.Context, orderID int) (bool, error)
	// ReviewVoucherPromotionalProducts 促销品凭证审核
	ReviewVoucherPromotionalProducts(c *gin.Context, req *api.ReimbursementAuditReq) (bool, error)
	// ReviewDataPromotionalProducts 促销品申请单收货单审核
	ReviewDataPromotionalProducts(c *gin.Context, req *api.ReimbursementAuditReq) (bool, error)
	// AddExpressInfo 添加快递信息
	AddExpressInfo(c *gin.Context, req *api.AddExpressInfoReq) (bool, error)
	// PromotionalProductsSplit 促销品订单拆分
	PromotionalProductsSplit(c *gin.Context, req *api.PromotionalProductsSplitReq) (bool, error)
}

type products struct {
	repo        dao.ReimbursementRepository
	policyRepo  dao.PolicyRepository
	balanceRepo dao.BalanceRepository
}

func NewProductsService(repo dao.ReimbursementRepository, policyRepo dao.PolicyRepository, balanceRepo dao.BalanceRepository) ProductsService {
	return &products{
		repo:        repo,
		policyRepo:  policyRepo,
		balanceRepo: balanceRepo,
	}
}

// GetPromotionalProductsQuickPickStat 获取促销产品快捷键统计
func (a *products) GetPromotionalProductsQuickPickStat(c *gin.Context, req *api.PromotionalProductsQuickPickStatReq) (*api.PromotionalProductsQuickPickStatResp, error) {
	return a.repo.GetPromotionalProductsQuickPickStat(c, req)
}

// GetPromotionalProductsDetail 获取推广产品详情
func (a *products) GetPromotionalProductsDetail(c *gin.Context, req *api.OrderReq) (*api.PromotionalProductsDetailResp, error) {
	data, err := a.repo.GetPromotionalProductsDetail(c, req)
	if err != nil {
		return nil, err
	}
	//apply_info
	applyInfo, err := a.repo.GetReimbursementApplyInfo(c, req.ID, "promotional_products")
	if err != nil {
		return nil, err
	}
	data.ApplicationInfo = applyInfo
	return data, nil
}

// InvalidPromotionalProducts 促销品申请单作废
func (a *products) InvalidPromotionalProducts(c *gin.Context, orderID int) (bool, error) {
	// 获取申请单详情
	order, err := a.repo.GetPromotionalProductsOrder(c, orderID, nil)
	if err != nil {
		return false, err
	}

	if order == nil {
		return false, errors.NewErr("未找到此记录信息, 无法作废")
	}

	// 获取关联的政策信息
	policyInfo, err := a.repo.GetPolicyByOrder(c, orderID, 1) // 1表示促销品类型
	if err != nil {
		return false, err
	}

	if policyInfo == nil {
		return false, errors.NewErr("此政策已过期, 无法作废")
	}

	// 检查政策是否已归档
	if policyInfo.Archive == 1 {
		return false, errors.NewErr("此政策已归档, 无法作废")
	}

	return a.repo.InvalidPromotionalProducts(c, orderID)
}

// ReviewVoucherPromotionalProducts 促销品凭证审核
func (a *products) ReviewVoucherPromotionalProducts(c *gin.Context, req *api.ReimbursementAuditReq) (bool, error) {
	uid := c.GetUint("uid")
	req.Uid = uid
	// 获取关联的政策信息
	policyInfo, err := a.repo.GetPolicyByOrder(c, req.ID, 1) // 1表示促销品类型
	if err != nil {
		return false, err
	}

	if policyInfo == nil {
		return false, errors.NewErr("此政策已过期")
	}

	// 检查政策是否已归档
	if policyInfo.Archive == 1 {
		return false, errors.NewErr("此政策已归档")
	}
	order, err := a.repo.GetPromotionalProductsOrder(c, req.ID, nil)
	if err != nil {
		return false, err
	}
	if order == nil {
		return false, errors.NewErr("申请单不存在")
	}
	err = a.repo.ReviewVoucherPromotionalProducts(c, req)
	if err != nil {
		return false, err
	}
	if req.Status == -1 {
		var url string
		if os.Getenv("GIN_MODE") != "release" {
			url = fmt.Sprintf("https://h5-yxtest.readboy.com/manage/reimbursement/record/detail?id=%d&type=promotional_products", req.ID)
		} else {
			url = fmt.Sprintf("https://h5-yx.readboy.com/manage/reimbursement/record/detail?id=%d&type=promotional_products", req.ID)
		}

		msg := fmt.Sprintf("申请单号%s已打回，请重新填写资料", order.SN)
		if err := reimbursementSMSPush(order.UID, msg, url); err != nil {
			// 这里只记录日志，不影响事务
			log.Printf("发送通知失败: %v", err)
		}
	}

	return true, nil
}

// ReviewDataPromotionalProducts 促销品申请单收货单审核
func (a *products) ReviewDataPromotionalProducts(c *gin.Context, req *api.ReimbursementAuditReq) (bool, error) {
	uid := c.GetUint("uid")
	req.Uid = uid
	// 获取关联的政策信息
	policyInfo, err := a.repo.GetPolicyByOrder(c, req.ID, 1) // 1表示促销品类型
	if err != nil {
		return false, err
	}

	if policyInfo == nil {
		return false, errors.NewErr("此政策已过期")
	}

	// 检查政策是否已归档
	if policyInfo.Archive == 1 {
		return false, errors.NewErr("此政策已归档")
	}
	order, err := a.repo.GetPromotionalProductsOrder(c, req.ID, nil)
	if err != nil {
		return false, err
	}
	if order == nil {
		return false, errors.NewErr("申请单不存在")
	}
	err = a.repo.ReviewDataPromotionalProducts(c, req, order)
	if err != nil {
		return false, err
	}
	if req.Status == -1 {
		var url string
		if os.Getenv("GIN_MODE") != "release" {
			url = fmt.Sprintf("https://h5-yxtest.readboy.com/manage/reimbursement/record/detail?id=%d&type=promotional_products", req.ID)
		} else {
			url = fmt.Sprintf("https://h5-yx.readboy.com/manage/reimbursement/record/detail?id=%d&type=promotional_products", req.ID)
		}

		msg := fmt.Sprintf("申请单号%s已打回，请重新填写资料", order.SN)
		if err := reimbursementSMSPush(order.UID, msg, url); err != nil {
			// 这里只记录日志，不影响事务
			log.Printf("发送通知失败: %v", err)
		}
	}

	return true, nil
}

// AddExpressInfo 添加快递信息
func (a *products) AddExpressInfo(c *gin.Context, req *api.AddExpressInfoReq) (bool, error) {
	// 获取关联的政策信息
	policyInfo, err := a.repo.GetPolicyByOrder(c, req.ID, 1) // 1表示促销品类型
	if err != nil {
		return false, err
	}

	if policyInfo == nil {
		return false, errors.NewErr("此政策已过期")
	}

	// 检查政策是否已归档
	if policyInfo.Archive == 1 {
		return false, errors.NewErr("此政策已归档")
	}
	order, err := a.repo.GetPromotionalProductsOrder(c, req.ID, nil)
	if err != nil {
		return false, err
	}
	if order == nil {
		return false, errors.NewErr("申请单不存在")
	}
	err = a.repo.AddExpressInfo(c, req)
	if err != nil {
		return false, err
	}
	return true, nil

}

// PromotionalProductsSplit 促销品订单拆分
func (a *products) PromotionalProductsSplit(c *gin.Context, req *api.PromotionalProductsSplitReq) (bool, error) {
	// 获取关联的政策信息
	policyInfo, err := a.repo.GetPolicyByOrder(c, req.OrderID, 1) // 1表示促销品类型
	if err != nil {
		return false, err
	}

	if policyInfo == nil {
		return false, errors.NewErr("此政策已过期")
	}

	// 检查政策是否已归档
	if policyInfo.Archive == 1 {
		return false, errors.NewErr("此政策已归档")
	}
	order, err := a.repo.ReimbursementApplyOrder(c, req.OrderID)
	if err != nil {
		return false, err
	}
	if order == nil {
		return false, errors.NewErr("申请单不存在")
	}
	// 检查分单数据格式
	var totalQuantity int64
	var totalAmount float64
	for _, item := range req.OrderSplitInfo {
		// 标准类型为数量时，检查数量和金额
		if policyInfo.StandardType == "standard_quantity" {
			if item.Quantity == 0 || item.Amount == 0 {
				return false, errors.NewErr("数据格式有误")
			}
			totalQuantity += item.Quantity
		} else {
			// 其他类型只检查金额
			if item.Amount == 0 {
				return false, errors.NewErr("数据格式有误")
			}
			// 非数量标准类型，数量设为0
			item.Quantity = 0
		}
		totalAmount += item.Amount
	}

	// 检查申请单类型是否为促销品
	if order.ApplyOrderType != "promotional_products" {
		return false, errors.NewErr("非促销品订单，操作错误")
	}

	// 检查订单状态
	if order.Status != 0 {
		return false, errors.NewErr("未核销订单才可拆单")
	}

	// 数量标准类型时，检查总数量是否一致
	if policyInfo.StandardType == "standard_quantity" {
		if totalQuantity != order.QuantityTotal {
			return false, errors.NewErr("数量合计与旧订单不等")
		}
	}

	// 检查总金额是否一致（保留2位小数）
	if math.Round(totalAmount*100)/100 != order.Amount {
		return false, errors.NewErr("金额合计与旧订单不等")
	}

	err = a.repo.PromotionalProductsSplit(c, req)
	if err != nil {
		return false, err
	}
	return true, nil
}
