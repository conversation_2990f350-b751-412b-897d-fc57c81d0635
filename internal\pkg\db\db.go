package db

import (
	"fmt"
	"log"
	"os"
	"time"

	zaplog "marketing/internal/pkg/log"

	"github.com/spf13/viper"
	"go.uber.org/zap"
	"gorm.io/driver/mysql"
	"gorm.io/gorm"
	"gorm.io/gorm/logger"
	"gorm.io/gorm/schema"
)

var (
	DB  *gorm.DB
	DBs = make(map[string]*gorm.DB) // A map to hold multiple DB connections
)

func Init() {
	fmt.Println("====init db====")
	dsnKeys := viper.GetStringSlice("mysql.dsn.keys") // Assume dsn keys are provided in the config
	fmt.Println("====init db====", dsnKeys)
	level := logger.Info
	if os.Getenv("GIN_MODE") == "release" {
		level = logger.Error
	}

	for _, key := range dsnKeys {
		dsn := viper.GetString("mysql.dsn." + key)
		if dsn == "" {
			log.Fatalf("DSN for key %s not found", key)
		}

		//sql日志
		newLogger := logger.New(
			log.New(os.Stdout, "", log.LstdFlags), // io writer（日志输出的目标，前缀和日志包含的内容——译者注）
			logger.Config{
				SlowThreshold:             time.Second, // 慢 SQL 阈值
				LogLevel:                  level,       // 日志级别
				IgnoreRecordNotFoundError: true,        // 忽略ErrRecordNotFound（记录未找到）错误
				Colorful:                  false,       // 禁用彩色打印
			},
		)

		var err error
		newDB, err := gorm.Open(mysql.New(mysql.Config{
			DSN:                       dsn,   // DSN data source name
			DefaultStringSize:         256,   // string 类型字段的默认长度
			DisableDatetimePrecision:  true,  // 禁用 datetime 精度，MySQL 5.6 之前的数据库不支持
			DontSupportRenameIndex:    true,  // 重命名索引时采用除并新建的方式，MySQL 5.7 之前的数据库和 MariaDB 不支持重命名索引
			DontSupportRenameColumn:   true,  // 用 `change` 重命名列，MySQL 8 之前的数据库和 MariaDB 不支持重命名列
			SkipInitializeWithVersion: false, // 根据当前 MySQL 版本自动配置
		}), &gorm.Config{
			Logger: newLogger,
			NamingStrategy: schema.NamingStrategy{
				SingularTable: true, // 表名使用单数形式
			},
		})

		if err != nil {
			zaplog.Fatal("mysql connect err", zap.Error(err))
		}
		sqlDB, _ := newDB.DB()
		sqlDB.SetMaxIdleConns(viper.GetInt("mysql.idle"))
		sqlDB.SetMaxOpenConns(viper.GetInt("mysql.open"))
		sqlDB.SetConnMaxLifetime(time.Second * time.Duration(viper.GetInt("mysql.lifeTime")))
		DBs[key] = newDB     // Store the DB instance in the map
		if key == "rbcare" { // 假设 "user" 是默认数据库
			DB = newDB
			fmt.Println("Default DB init...")
		}
	}
}

// GetDB 获取数据库连接，传入键来获取相应的 DB，未传入时返回默认的 DB
func GetDB(key ...string) *gorm.DB {
	if len(key) == 0 || key[0] == "" {
		return DB // 如果没有传入键，则返回默认 DB
	}
	if db, ok := DBs[key[0]]; ok {
		return db
	}
	zaplog.Panic(fmt.Sprintf("No database connection found for key: %s", key[0]))
	return nil
}

func Close() {
	for key, db := range DBs {
		sqlDB, err := db.DB()
		if err != nil {
			zaplog.Error("Failed to retrieve database handle", zap.String("key", key), zap.Error(err))
			continue
		}
		err = sqlDB.Close()
		if err != nil {
			zaplog.Error("Failed to close database connection", zap.String("key", key), zap.Error(err))
		} else {
			zaplog.Info("Closed database connection", zap.String("key", key))
		}
	}
}
