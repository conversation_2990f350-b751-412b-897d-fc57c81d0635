package operation

const (
	ArticleTagNamesSeparator = "@#"
)

const (
	ArticleShareSlug = "operation_article"
)

const (
	LikeTargetArticle = iota + 1
	LikeTargetComment
)

const (
	ViewTargetArticle = iota + 1
)

const (
	OssBaseUrl = "https://dt1.readboy.com/"
)

const (
	ArticleCommentSettingNoNeedAudit = 1 + iota
	ArticleCommentSettingNeedAudit
	ArticleCommentSettingBanned
)

const (
	ArticleAttachmentTypeImage = 1 + iota
	ArticleAttachmentTypeVideo
	ArticleAttachmentTypeLink
	ArticleAttachmentTypeFile
)

// ReqOpArticleParam 查询运营文章参数
type ReqOpArticleParam struct {
	Keyword    string `json:"keyword"`     // 关键字
	Enabled    int    `json:"enabled"`     // 是否发布 0:未发布 1:发布
	CreatorID  uint   `json:"creator_id"`  // 作者id
	CategoryID uint   `json:"category_id"` // 分类id
	OrderBy    string `json:"order_by"`    // 排序字段
	Order      string `json:"order"`       // 排序顺序 asc:升序 desc:降序
	PageNum    int    `json:"page_num"`    // 页码
	PageSize   int    `json:"page_size"`   // 页幅
}

// ReqCreateArticleParam 创建运营文章参数
type ReqCreateArticleParam struct {
	ID                  uint               `json:"id" form:"id" binding:"-"`                                                                // id
	Title               string             `json:"title" form:"title" binding:"required"`                                                   // 标题
	Content             string             `json:"content" form:"content"`                                                                  // 内容
	Sleight             string             `json:"sleight" form:"sleight"`                                                                  // 话术
	CategoryID          uint               `json:"category_id" form:"category_id" binding:"required,min=1"`                                 // 分类id
	PublisherID         uint               `json:"publisher_id" form:"publisher_id" binding:"required,min=1"`                               // 运营号id
	Shareable           uint8              `json:"shareable" form:"shareable" binding:"required,oneof=0 1"`                                 // 是否可以分享 0:不可分享 1:分享
	WeWorkShareable     uint8              `json:"wework_shareable" form:"wework_shareable" binding:"required,oneof=0 1"`                   // 是否可通过企业微信分享 0:不可分享 1:分享
	CommentSetting      uint8              `json:"comment_setting" form:"comment_setting" binding:"required,oneof=1 2 3"`                   // 是否可以评论 1:无须审核 2:审核发布 3:不可评论
	Enabled             uint8              `json:"enabled" form:"enabled" binding:"required,oneof=0 1"`                                     // 是否发布 0:未发布 1:发布
	MarketingDownloadID uint               `json:"marketing_download_id" form:"marketing_download_id"`                                      // marketing_download表同步id
	AttachmentType      uint8              `json:"attachment_type" form:"attachment_type" binding:"required_with=Attachment,oneof=1 2 3 4"` // 附件类型 1:图片 2:视频 3:链接 4:图片
	Attachment          *ArticleAttachment `json:"attachment,omitempty" form:"attachment"`                                                  // 附件内容
	CreatedBy           uint               `json:"created_by" form:"created_by"`                                                            // 创建人id
	TagIds              []uint             `json:"tag_ids" binding:"required"`                                                              // 标签id
	SyncMarketing       uint8              `json:"sync_marketing" binding:"required,oneof=0 1"`                                             // 是否同步 0:不同步 1:同步
	MarketingEnabled    uint8              `json:"marketing_enabled" binding:"required_if=sync_marketing 1,oneof=0 1"`                      // marketing是否发布 0:未发布 1:发布
	MarketingCategoryId uint               `json:"marketing_category_id" binding:"required_if=sync_marketing 1,min=1"`                      // marketing分类id
}

// OpArticleInfo 运营文章
type OpArticleInfo struct {
	ID             uint                `json:"id"`                   // id
	Title          string              `json:"title"`                // 标题
	Content        string              `json:"content"`              // 内容
	CommentSetting uint8               `json:"comment_setting"`      // 是否可以评论 1:无须审核 2:审核发布 3:不可评论
	CategoryID     uint                `json:"category_id"`          // 分类id
	CategoryName   string              `json:"category_name"`        // 分类名称
	Sleight        string              `json:"sleight"`              // 话术
	Shareable      uint8               `json:"shareable"`            // 是否可以分享 0:不可分享 1:分享
	Tags           []*OpArticleTagInfo `json:"tags"`                 // 标签列表
	NumShares      uint                `json:"num_shares"`           // 分享数
	NumComments    uint                `json:"num_comments"`         // 评论数
	NumLikes       uint                `json:"num_likes"`            // 点赞数
	NumViews       uint                `json:"num_views"`            // 浏览数
	NumDownloads   uint                `json:"num_downloads"`        // 下载数
	Creator        string              `json:"creator"`              // 创建者名称
	CreatorID      uint                `json:"creator_id"`           // 创建者id
	PublisherId    int                 `json:"publisher_id"`         // 运营号id
	PublisherName  string              `json:"publisher_name"`       // 运营号名称
	AttachmentType uint8               `json:"attachment_type"`      // 附件类型 1:图片 2:视频 3:链接 4:图片
	Attachment     OpArticleAttachment `json:"attachment,omitempty"` // 附件内容
	Enabled        uint8               `json:"enabled"`              // 是否发布 0:未发布 1:发布
	CreatedAt      string              `json:"created_at"`           // 创建时间
	UpdatedAt      string              `json:"updated_at"`           // 修改时间
}

// OpArticleComment 运营评论
type OpArticleComment struct {
	Id         uint               `json:"id"`          // id
	Content    string             `json:"content"`     // 内容
	Files      []string           `json:"files"`       // 附件
	CreateName string             `json:"create_name"` // 评论名称
	ReplyName  string             `json:"reply_name"`  // 回复名称
	Visible    uint               `json:"visible"`     // 是否可见
	Selected   uint               `json:"selected"`    // 是否精选
	Top        uint               `json:"top"`         // 是否置顶
	Discarded  uint               `json:"discarded"`   // 是否放入回收站
	ChildList  []OpArticleComment `json:"child_list"`  // 子评论列表
}

// OpArticleCommentStatistics 运营评论统计
type OpArticleCommentStatistics struct {
	ArticleId  uint   `json:"article_id"`  // id
	Title      string `json:"title"`       // 标题
	CreateTime string `json:"create_time"` // 创建时间
	CommentNum int    `json:"comment_num"` // 评论数
}

// OpArticleTagInfo 运营文章标签信息
type OpArticleTagInfo struct {
	Id         uint   `json:"id"`          // 标签id
	Name       string `json:"name"`        // 标签名称
	ArticleNum int    `json:"article_num"` // 文章数量
	UpdateTime string `json:"update_time"` // 修改时间
}

// OpArticleAttachment 运营文章附件信息
type OpArticleAttachment struct {
	Files        []string `json:"files"`
	Covers       []string `json:"covers"`
	Title        string   `json:"title"`
	Desc         string   `json:"desc"`
	Link         string   `json:"link"`
	Downloadable uint8    `json:"downloadable"`
}

// OpUserInfo 运营用户信息
type OpUserInfo struct {
	Id       uint   `json:"id"`
	Name     string `json:"name"`
	Username string `json:"username"`
	Blocked  uint8  `json:"blocked"`
}

// OpPublisherInfo 运营号信息
type OpPublisherInfo struct {
	Id         uint                  `json:"id"`
	Name       string                `json:"name"`
	Avatar     string                `json:"avatar"`
	ArticleNum int                   `json:"article_num"`
	AuthorList []OpPublisherUserInfo `json:"author_list"`
}

// OpPublisherUserInfo 运营号用户信息
type OpPublisherUserInfo struct {
	PublisherId uint   `json:"-"`
	Id          uint   `json:"id"`
	Name        string `json:"name"`
}

// 内容

type Article struct {
	ID                  uint               `json:"id" form:"id" binding:"-"`
	Title               string             `json:"title" form:"title" binding:"required"`
	Content             string             `json:"content" form:"content"`
	Sleight             string             `json:"sleight" form:"sleight"`
	CategoryID          uint               `json:"category_id" form:"category_id" binding:"required,min=1"`
	PublisherID         uint               `json:"publisher_id" form:"publisher_id" binding:"required,min=1"`
	Shareable           *uint8             `json:"shareable" form:"shareable" binding:"required,oneof=0 1"`
	WeWorkShareable     *uint8             `json:"wework_shareable" form:"wework_shareable" binding:"required,oneof=0 1"`
	CommentSetting      uint8              `json:"comment_setting" form:"comment_setting" binding:"required,oneof=1 2 3"`
	Enabled             *uint8             `json:"enabled" form:"enabled" binding:"required,oneof=0 1"`
	MarketingDownloadID uint               `json:"marketing_download_id" form:"marketing_download_id"`
	AttachmentType      uint8              `json:"attachment_type" form:"attachment_type" binding:"required_with=Attachment,oneof=1 2 3 4"`
	Attachment          *ArticleAttachment `json:"attachment,omitempty" form:"attachment"`
	CreatedBy           uint               `json:"created_by" form:"created_by"`
	CreatedAt           string             `json:"created_at" form:"created_at"`
	UpdatedAt           string             `json:"updated_at" form:"updated_at"`
}

type ArticleListReq struct {
	Page       int    `json:"page" form:"page"`
	PageSize   int    `json:"page_size" form:"page_size"`
	CategoryID uint   `json:"category_id" form:"category_id"`
	Keyword    string `json:"keyword" form:"keyword"`
	CreatorID  uint   `json:"creator_id" form:"creator_id"`
	Enabled    *uint8 `json:"enabled" form:"enabled"` // null|0|1
	OrderBy    string `json:"order_by" form:"order_by" binding:"oneof=created_at updated_at"`
	Order      string `json:"order" form:"order" binding:"oneof=asc desc"`
}

type ArticleListRspItem struct {
	ID             uint               `json:"id"`
	Title          string             `json:"title"`
	Content        string             `json:"content"`
	CommentSetting uint8              `json:"comment_setting"`
	CategoryID     uint               `json:"category_id"`
	CategoryName   string             `json:"category_name"`
	Sleight        string             `json:"sleight"`
	Shareable      uint8              `json:"shareable"`
	Tags           []ArticleTagsItem  `json:"tags"`
	NumShares      uint               `json:"num_shares"`
	NumComments    uint               `json:"num_comments"`
	NumLikes       uint               `json:"num_likes"`
	NumViews       uint               `json:"num_views"`
	NumDownloads   uint               `json:"num_downloads"`
	Creator        string             `json:"creator"`
	CreatorID      uint               `json:"creator_id"`
	PublisherName  string             `json:"publisher_name"`
	AttachmentType uint8              `json:"attachment_type"`
	Attachment     *ArticleAttachment `json:"attachment,omitempty"`
	Enabled        uint8              `json:"enabled"`
	CreatedAt      string             `json:"created_at"`
	UpdatedAt      string             `json:"updated_at"`
}

type ArticleTagsItem struct {
	ID   uint   `json:"id"`
	Name string `json:"name"`
}

type ArticleAttachment struct {
	Files        []string `json:"files"`
	Covers       []string `json:"covers"`
	Title        string   `json:"title"`
	Desc         string   `json:"desc"`
	Link         string   `json:"link"`
	Downloadable uint8    `json:"downloadable"`
}

type ArticleDetailRsp struct {
	Article
	Tags                []ArticleTagsItem `json:"tags"`
	MarketingCategoryID uint              `json:"marketing_category_id"`
	MarketingEnabled    uint8             `json:"marketing_enabled"`
}

type ArticleCreateReq struct {
	Article
	TagIds              []uint `json:"tag_ids" binding:"required"`
	SyncMarketing       uint8  `json:"sync_marketing" binding:"required,oneof=0 1"`
	MarketingEnabled    uint8  `json:"marketing_enabled" binding:"required_if=SyncMarketing 1,oneof=0 1"`
	MarketingCategoryId uint   `json:"marketing_category_id" binding:"required_if=SyncMarketing 1,min=1"`
}

type ArticleListRsp struct {
	List  []ArticleListRspItem
	Total int64
}

type ArticleCreatorsRsp struct {
	List []ArticleCreatorsRspItem
}

type ArticleCreatorsRspItem struct {
	ID   uint   `json:"id"`
	Name string `json:"name"`
}

// 类别

type ArticleCategoryListRsp struct {
	List []ArticleCategoryListRspItem
}

type ArticleCategoryListRspItem struct {
	ID          uint                         `json:"id"`
	ParentID    uint                         `json:"parent_id"`
	Name        string                       `json:"name"`
	Enabled     uint8                        `json:"enabled"`
	Order       int                          `json:"order"`
	NumArticles uint                         `json:"num_articles"`
	Children    []ArticleCategoryListRspItem `json:"children"`
}

// 标签

type ArticleTagListRsp struct {
	List []ArticleTagListRspItem
}

type ArticleTagListRspItem struct {
	ID          uint   `json:"id"`
	Name        string `json:"name"`
	NumArticles uint   `json:"num_articles"`
	UpdatedAt   string `json:"updated_at"`
}

type ArticleCommentListReq struct {
	ID        uint   `json:"id" form:"id" binding:"required,min=1"`
	Page      int    `json:"page" form:"page"`
	PageSize  int    `json:"page_size" form:"page_size"`
	ReplySize int    `json:"reply_size" form:"reply_size"`
	Type      string `json:"type" form:"type" binding:"required,oneof=all enabled top discarded"`
	Creator   string `json:"creator" form:"creator"`
	OrderBy   string `json:"order_by" form:"order_by" binding:"required,oneof=created_at num_likes num_comments"`
	Order     string `json:"order" form:"order" binding:"required,oneof=asc desc"`
}

// 评论

type ArticleCommentListRsp struct {
	List []ArticleCommentListRspItem
}

type ArticleCommentListRspItem struct {
	ID         int             `json:"id"`
	Content    string          `json:"content"`
	ParentID   int             `json:"parent_id"`
	ReplyTo    string          `json:"reply_to,omitempty"`
	Attachment []string        `json:"attachment"`
	Enabled    int             `json:"enabled"`
	Top        int             `json:"top"`
	Discarded  int             `json:"discarded"`
	NumLikes   int             `json:"num_likes"`
	CreatedAt  string          `json:"created_at"`
	CreatedBy  int             `json:"created_by"`
	Creator    CommentCreator  `json:"creator"`
	Replies    *CommentReplies `json:"replies"`
}
type CommentCreator struct {
	Avatar      string `json:"avatar"`
	ID          int    `json:"id"`
	Name        string `json:"name"`
	NumComments int    `json:"num_comments"`
}

type CommentReplies struct {
	Count int `json:"count"`
	List  []ArticleCommentListRspItem
}
