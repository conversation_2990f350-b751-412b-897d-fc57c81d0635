server:
  name: marketing-admin
  port: 8088
  mode: debug
  secretKey: sdfewgsdjfwien@3werwe

mysql:
  idle: 10
  open: 100
  lifeTime: 3600
  default: rbcare
  rbcareData: rbcare_data
  dsn:
    keys:
      - rbcare
      - post_repair
      - rbcare_data
    rbcare: "rbcare_test:1AHV6va4begLztln@tcp(rm-wz9049tx2592u4e6pho.mysql.rds.aliyuncs.com)/rbcare_test?charset=utf8mb4&parseTime=True&loc=Local"
    post_repair: "rbcare_test:1AHV6va4begLztln@tcp(rm-wz9049tx2592u4e6pho.mysql.rds.aliyuncs.com)/post_repair_test?charset=utf8mb4&parseTime=True&loc=Local"
    rbcare_data:  "rbcare_test:1AHV6va4begLztln@tcp(rm-wz9049tx2592u4e6pho.mysql.rds.aliyuncs.com)/rbcare_test?charset=utf8mb4&parseTime=True&loc=Local"

redis:
  addr: "47.106.174.126:6379"
  password: ILq38ppdVT96TDtj
  db: 7

weCom:
  corpID: ww956a5ce01e8f69af
  corpSecret: g-ZkH5XTm9k0lzMZNU8jsbpk33Neax60WGxk3387xEg
  contactsSecret: x-ymEUqRjm30CqqwZkNOEtdVZEr5Y60B_UUfwahkGe8

adminWeCom:
  corpID: ww956a5ce01e8f69af
  corpSecret: g-ZkH5XTm9k0lzMZNU8jsbpk33Neax60WGxk3387xEg

oss:
  region: cn-shenzhen
  bucket: acres
  path : marketing
  url : https://static.readboy.com/

repair:
  baseURL: http://api-repair-test.readboy.com
  appID: yx.readboy.com
  appKey: 710eb100ff2120cbcde0d78e532de8cg