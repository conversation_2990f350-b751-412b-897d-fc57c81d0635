package utils

import (
	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
	"marketing/internal/pkg/e"
)

// PaginateQuery 是一个通用的分页查询方法
// db: 数据库连接对象
// c: Gin 上下文对象
func PaginateQuery[T any](db *gorm.DB, c *gin.Context, data *[]T) (*[]T, int64, int, int, error) {
	// 从请求中获取分页参数
	page := e.ReqParamInt(c, "page_num", 1)
	pageSize := e.ReqParamInt(c, "page_size", 20)

	// 计算偏移量
	offset := (page - 1) * pageSize

	// 确保 data 被初始化
	if data == nil {
		data = &[]T{}
	}

	// 查询数据库，获取分页记录，并将结果存储在 data 切片中
	if dbErr := db.Offset(offset).Limit(pageSize).Find(data).Error; dbErr != nil {
		return nil, 0, 0, 0, dbErr
	}

	// 查询总记录数
	var total int64
	if dbErr := db.Model(new(T)).Count(&total).Error; dbErr != nil {
		return nil, 0, 0, 0, dbErr
	}

	return data, total, page, pageSize, nil
}

func PaginateQueryV1[T any](db *gorm.DB, pageNum, pageSize int, data *[]T) (*[]T, int64) {
	if pageNum < 1 {
		pageNum = 1
	}

	if pageSize < 1 {
		pageSize = 20
	}

	// 确保 data 被初始化
	if data == nil {
		data = &[]T{}
	}

	// 查询总记录数
	var total int64
	if dbErr := db.Model(new(T)).Count(&total).Error; dbErr != nil {
		return nil, 0
	}

	// 查询数据库，获取分页记录，并将结果存储在 data 切片中
	if dbErr := db.Offset((pageNum - 1) * pageSize).Limit(pageSize).Find(data).Error; dbErr != nil {
		return nil, 0
	}

	return data, total
}
