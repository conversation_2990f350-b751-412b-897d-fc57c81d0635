package api

import "marketing/internal/consts"

// PaginationParams 分页参数
type PaginationParams struct {
	Page     int `form:"page" json:"page"`
	PageSize int `form:"page_size" json:"page_size"`
}

// PaginationListResp 分页参数
// PaginationListResp 分页响应结构
type PaginationListResp struct {
	Total    int64 `json:"total"`
	Page     int   `json:"page"`
	PageSize int   `json:"page_size"`
	Data     any   `json:"data"` // 使用泛型类型 T
}

type PagedResponse[T any] struct {
	Total    int64 `json:"total"`
	Page     int   `json:"page"`
	PageSize int   `json:"page_size"`
	Data     []T   `json:"data"` // 使用泛型类型 T
}

// SetDefaults 设置默认分页值
func (p *PaginationParams) SetDefaults() {
	if p.Page == 0 {
		p.Page = consts.DefaultPage
	}
	if p.PageSize == 0 {
		p.PageSize = consts.DefaultPageSize
	}
}

func NewPagedResponse[T any](data []T, total int64, page int, pageSize int) *PagedResponse[T] {
	return &PagedResponse[T]{
		Total:    total,
		Page:     page,
		PageSize: pageSize,
		Data:     data,
	}
}
