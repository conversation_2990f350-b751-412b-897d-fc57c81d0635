package config

import (
	"log"

	"github.com/fsnotify/fsnotify"
	"github.com/spf13/viper"
)

type Config struct {
	Server struct {
		Name      string
		Port      int
		Mode      string
		SecretKey string
	}
	MySQL struct {
		Idle       int
		Open       int
		LifeTime   int
		Default    string
		RbcareData string
		DSN        struct {
			Keys   []string
			Rbcare string
		}
	}
	Redis struct {
		Addr     string
		Password string
		DB       int
	}
	WeCom struct {
		CorpID         string
		CorpSecret     string
		ContactsSecret string
	}
	AdminWeCom struct {
		CorpID     string
		CorpSecret string
	}
	OSS struct {
		Region string
		Bucket string
		Path   string
		Url    string
	}
	Repair struct {
		BaseURL string
		AppID   string
		AppKey  string
	}
}

func NewConfig() *Config {
	viper.SetConfigName("config")
	viper.SetConfigType("yaml")
	viper.AddConfigPath("./conf")
	viper.AddConfigPath("../../conf")

	viper.WatchConfig() // Automatically reload the config file when it changes
	viper.OnConfigChange(func(e fsnotify.Event) {
		log.Printf("Config file changed: %s", e.Name)
	})

	if err := viper.ReadInConfig(); err != nil {
		log.Fatalf(err.Error())
	}

	var config Config
	if err := viper.Unmarshal(&config); err != nil {
		log.Fatalf(err.Error())
	}

	return &config
}
