package endpoint

import (
	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
	"marketing/internal/model"
)

type EndpointImageDao interface {
	GetByID(c *gin.Context, id uint) (*model.EndpointImageApply, error)
	Update(c *gin.Context, id uint, updateData map[string]interface{}) error
}

type endpointImage struct {
	db *gorm.DB
}

func NewEndpointImageDao(db *gorm.DB) EndpointImageDao {
	return &endpointImage{db: db}
}

func (dao *endpointImage) GetByID(c *gin.Context, id uint) (*model.EndpointImageApply, error) {
	var apply model.EndpointImageApply
	err := dao.db.WithContext(c).Where("id = ?", id).First(&apply).Error
	if err != nil {
		return nil, err
	}
	return &apply, nil
}

func (dao *endpointImage) Update(c *gin.Context, id uint, updateData map[string]interface{}) error {
	return dao.db.WithContext(c).Model(&model.EndpointImageApply{}).Where("id = ?", id).Updates(updateData).Error
}
