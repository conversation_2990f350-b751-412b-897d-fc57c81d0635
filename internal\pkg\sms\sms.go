package sms

import (
	"crypto/md5"
	"encoding/hex"
	"encoding/json"
	"fmt"
	"marketing/internal/pkg/log"
	"math/rand"
	"net/url"
	"regexp"
	"strconv"
	"time"

	"github.com/go-resty/resty/v2"
)

const serverAddress = "https://api-sms.readboy.com/index.php?s=/Sms/Api/send"
const HttpTimeout = 30 * time.Second

type Response struct {
	Code    string `json:"code"`
	Message string `json:"message"`
}

func makeURLCode(recNum, signName, templateCode string, templateParam map[string]any) (string, string) {
	ts := strconv.FormatInt(time.Now().Unix(), 10)
	key := "8e517d999976168979f81dc73d010c90"
	code := fmt.Sprintf("%06d", rand.Intn(900000)+100000)
	paramStringJSON, _ := json.Marshal(templateParam)
	authKey := fmt.Sprintf("%s-%s-%s", ts, code, md5Hash(fmt.Sprintf("%s-%s-%s", ts, code, key)))

	parameters := url.Values{}
	parameters.Set("authKey", authKey)
	parameters.Set("appName", "care.readboy.com")
	parameters.Set("signName", signName)
	parameters.Set("templateCode", templateCode)
	parameters.Set("phoneNumber", recNum)
	parameters.Set("templateParam", string(paramStringJSON))

	requestURL := fmt.Sprintf("%s&%s", serverAddress, parameters.Encode())
	return requestURL, code
}

func md5Hash(text string) string {
	hash := md5.Sum([]byte(text))
	return hex.EncodeToString(hash[:])
}

func SendSMS(phone, signName, templateCode string, templateParam map[string]any) (bool, string) {
	// 验证手机号码格式
	phoneRegex := regexp.MustCompile(`^1[3-9]\d{9}$`)
	if !phoneRegex.MatchString(phone) {
		log.Error(fmt.Sprintf("Invalid phone number: %s\n", phone))
		return false, "无效的手机号码"
	}

	client := resty.New().SetTimeout(HttpTimeout)
	requestURL, code := makeURLCode(phone, signName, templateCode, templateParam)
	msg := "发送验证码失败，请重新发送"
	data := false

	fmt.Println(requestURL)
	resp, err := client.R().
		Get(requestURL)

	if err != nil {
		log.Error(fmt.Sprintf("phone code send error: phone=%s, code=%s, error=%s\n", phone, code, err))
		return false, msg
	}
	fmt.Println(string(resp.Body()))

	var ret Response
	if err := json.Unmarshal(resp.Body(), &ret); err != nil {
		log.Error(fmt.Sprintf("phone code send error: phone=%s, code=%s, error=%s\n", phone, code, err))
		return false, msg
	}

	if ret.Code == "200" {
		msg = "success"
		data = true
	} else {
		log.Error(fmt.Sprintf("phone code send error: phone=%s, code=%s, response:%s %s\n ", phone, code, ret.Code, ret.Message))
		msg = ret.Message
		data = false
	}

	return data, msg
}
