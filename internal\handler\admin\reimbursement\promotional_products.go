package reimbursement

import (
	"github.com/gin-gonic/gin"
	api "marketing/internal/api/reimbursement"
	"marketing/internal/handler"
	"marketing/internal/pkg/errors"
	service "marketing/internal/service/reimbursement"
	"strconv"
)

// PromotionalProducts handles reimbursement application related requests
type PromotionalProducts interface {
	// GetPromotionalProductsQuickPickStat returns quick pick statistics for promotional products
	GetPromotionalProductsQuickPickStat(c *gin.Context)
	// GetPromotionalProductsHeader returns promotional products header
	GetPromotionalProductsHeader(c *gin.Context)
	// GetPromotionalProductsList returns promotional products list
	GetPromotionalProductsList(c *gin.Context)
	// GetPromotionalProductsDetail returns promotional products detail
	GetPromotionalProductsDetail(c *gin.Context)
	// InvalidPromotionalProducts handles advert expense invalidation
	InvalidPromotionalProducts(c *gin.Context)
	// ReviewVoucherPromotionalProducts handles promotional products voucher review
	ReviewVoucherPromotionalProducts(c *gin.Context)
	//ReviewDataPromotionalProducts handles promotional products review
	ReviewDataPromotionalProducts(c *gin.Context)
	// AddExpressInfo uploads express information for promotional products
	AddExpressInfo(c *gin.Context)
	//PromotionalProductsSplit handles promotional products split
	PromotionalProductsSplit(c *gin.Context)
}

type products struct {
	policyService        service.PolicyService
	reimbursementService service.ProductsService
}

// NewPromotionalProducts creates a new ApplyHandler
func NewPromotionalProducts(policyService service.PolicyService, reimbursementService service.ProductsService) PromotionalProducts {
	return &products{
		policyService:        policyService,
		reimbursementService: reimbursementService,
	}
}

// GetPromotionalProductsQuickPickStat returns quick pick statistics for promotional products
func (a *products) GetPromotionalProductsQuickPickStat(c *gin.Context) {
	// Parse request parameters
	var req api.PromotionalProductsQuickPickStatReq
	if err := c.ShouldBind(&req); err != nil {
		handler.Error(c, err)
		return
	}

	// Call service to get promotional products quick pick statistics
	stats, err := a.reimbursementService.GetPromotionalProductsQuickPickStat(c, &req)
	if err != nil {
		handler.Error(c, err)
		return
	}

	// Return statistics
	handler.Success(c, stats)
}

// GetPromotionalProductsHeader 获取推广产品表头
func (a *products) GetPromotionalProductsHeader(c *gin.Context) {
	// 获取policy_id参数
	policyIDStr := c.Query("policy_id")
	if policyIDStr == "" {
		handler.Error(c, errors.NewErr("请选择指定政策"))
		return
	}

	// 转换为整数
	policyID, err := strconv.Atoi(policyIDStr)
	if err != nil {
		handler.Error(c, errors.NewErr("政策ID格式错误"))
		return
	}

	// 调用服务获取表头数据
	header, err := a.policyService.GetPromotionalProductsHeader(c, policyID)
	if err != nil {
		handler.Error(c, err)
		return
	}

	handler.Success(c, header)
}

// GetPromotionalProductsList 获取推广产品列表
func (a *products) GetPromotionalProductsList(c *gin.Context) {
	var req api.PromotionalProductsListReq

	// 绑定查询参数
	if err := c.ShouldBindQuery(&req); err != nil {
		handler.Error(c, errors.NewErr("参数错误: "+err.Error()))
		return
	}

	// 验证必需参数
	if req.PolicyID == 0 {
		handler.Error(c, errors.NewErr("请选择指定政策"))
		return
	}

	// 调用服务获取数据
	resp, err := a.policyService.GetPromotionalProductsList(c, &req)
	if err != nil {
		handler.Error(c, err)
		return
	}

	handler.Success(c, resp)
}

// GetPromotionalProductsDetail 获取推广产品详情
func (a *products) GetPromotionalProductsDetail(c *gin.Context) {
	var req api.OrderReq

	// 绑定查询参数
	if err := c.ShouldBindQuery(&req); err != nil {
		handler.Error(c, errors.NewErr("参数错误: "+err.Error()))
		return
	}

	// 调用服务获取详情数据
	detail, err := a.reimbursementService.GetPromotionalProductsDetail(c, &req)
	if err != nil {
		handler.Error(c, err)
		return
	}

	if detail == nil {
		handler.Error(c, errors.NewErr("未找到数据"))
		return
	}

	handler.Success(c, detail)
}

// InvalidPromotionalProducts 促销品申请单作废
func (a *products) InvalidPromotionalProducts(c *gin.Context) {
	var req api.OrderReq
	if err := c.ShouldBind(&req); err != nil {
		handler.Error(c, errors.NewErr("参数错误"))
		return
	}

	// 执行作废操作
	success, err := a.reimbursementService.InvalidPromotionalProducts(c, req.ID)
	if err != nil {
		handler.Error(c, err)
		return
	}

	if success {
		handler.Success(c)
	} else {
		handler.Error(c, errors.NewErr("系统出错"))
	}
}

// ReviewVoucherPromotionalProducts 凭证审核
func (a *products) ReviewVoucherPromotionalProducts(c *gin.Context) {
	var req api.ReimbursementAuditReq
	if err := c.ShouldBind(&req); err != nil {
		handler.Error(c, errors.NewErr("参数错误"))
		return
	}

	// 验证申请单类型
	if req.Status == -1 && req.Remark == "" {
		handler.Error(c, errors.NewErr("请填写备注"))
		return
	}

	// 执行审核操作
	success, err := a.reimbursementService.ReviewVoucherPromotionalProducts(c, &req)
	if err != nil {
		handler.Error(c, err)
		return
	}

	if success {
		handler.Success(c)
	} else {
		handler.Error(c, errors.NewErr("系统出错"))
	}
}

// ReviewDataPromotionalProducts 促销品申请单收货单审核
func (a *products) ReviewDataPromotionalProducts(c *gin.Context) {
	var req api.ReimbursementAuditReq
	if err := c.ShouldBind(&req); err != nil {
		handler.Error(c, errors.NewErr("参数错误"))
		return
	}

	// 验证申请单类型
	if req.Status == -1 && req.Remark == "" {
		handler.Error(c, errors.NewErr("请填写备注"))
		return
	}

	// 执行审核操作
	success, err := a.reimbursementService.ReviewDataPromotionalProducts(c, &req)
	if err != nil {
		handler.Error(c, err)
		return
	}

	if success {
		handler.Success(c)
	} else {
		handler.Error(c, errors.NewErr("系统出错"))
	}
}

// AddExpressInfo 上传收货单（添加快递信息）
func (a *products) AddExpressInfo(c *gin.Context) {
	var req api.AddExpressInfoReq
	if err := c.ShouldBind(&req); err != nil {
		handler.Error(c, errors.NewErr("参数错误: "+err.Error()))
		return
	}

	// 调用服务添加快递信息
	success, err := a.reimbursementService.AddExpressInfo(c, &req)
	if err != nil {
		handler.Error(c, err)
		return
	}

	if success {
		handler.Success(c)
	} else {
		handler.Error(c, errors.NewErr("系统出错"))
	}
}

// PromotionalProductsSplit 促销品订单拆分
func (a *products) PromotionalProductsSplit(c *gin.Context) {
	// 定义请求结构体
	var req api.PromotionalProductsSplitReq
	if err := c.ShouldBind(&req); err != nil {
		handler.Error(c, errors.NewErr("参数错误: "+err.Error()))
		return
	}

	// 基础参数校验
	if req.OrderID == 0 {
		handler.Error(c, errors.NewErr("请选择需要分单的订单"))
		return
	}

	if len(req.OrderSplitInfo) == 0 {
		handler.Error(c, errors.NewErr("请选择需要分单的订单和对应金额"))
		return
	}

	// 验证通过后，调用service处理业务逻辑
	success, err := a.reimbursementService.PromotionalProductsSplit(c, &req)
	if err != nil {
		handler.Error(c, err)
		return
	}

	if success {
		handler.Success(c, nil)
	} else {
		handler.Error(c, errors.NewErr("系统错误"))
	}
}
