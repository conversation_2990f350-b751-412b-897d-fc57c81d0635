package system

import (
	"errors"
	"fmt"
	"marketing/internal/config"
	"marketing/internal/consts"
	dao "marketing/internal/dao/admin_user"
	"marketing/internal/model"
	appError "marketing/internal/pkg/errors"
	"marketing/internal/pkg/log"
	"marketing/internal/pkg/wecom"
	"os"
	"slices"
	"sort"

	"go.uber.org/zap"

	"github.com/gin-gonic/gin"
	"github.com/spf13/cast"
	"gorm.io/gorm"
)

const (
	QWDeptRoot         = 1       // 企业微信根部门ID
	QWDeptAgencyMin    = 1000000 // 一级经销商部门ID起始值
	QWDeptAgencyMax    = 1999999 // 一级经销商部门ID结束值
	QWDeptSubagencyMin = 2000000 // 二级经销商部门ID起始值
	QWDeptSubagencyMax = 2999999 // 二级经销商部门ID结束值
	QWDeptEndpointMin  = 3000000 // 终端部门ID起始值
	QWDeptEndpointMax  = 3999999 // 终端部门ID结束值
)

type Department struct {
	ID        uint
	ParentID  uint
	Level     int
	QwPartyid uint
	Name      string
	Type      string
	Main      bool
	Belong    bool
	IsLeader  bool
}

// Departments 部门列表排序
type Departments []Department

// Len 实现 sort.Interface 接口的 Len 方法
func (d Departments) Len() int {
	return len(d)
}

// Less 实现 sort.Interface 接口的 Less 方法，按 Level 从小到大排序
func (d Departments) Less(i, j int) bool {
	return d[i].Level < d[j].Level
}

// Swap 实现 sort.Interface 接口的 Swap 方法
func (d Departments) Swap(i, j int) {
	d[i], d[j] = d[j], d[i]
}

type WecomSyncService interface {
	SyncUser(c *gin.Context, id uint, agencyID uint) (string, error)
	AddUserTag(c *gin.Context, tag int, QWUserIDs []string) error
	DeleteUserTag(c *gin.Context, tag int, QWUserIDs []string) error
	SyncResourceGroupDepartments(c *gin.Context, name string, parentID, qwID int) error
}

type syncService struct {
	db          *gorm.DB
	userDao     dao.UserDao
	wecomClient *wecom.Client
}

func NewSyncService(db *gorm.DB, userDao dao.UserDao, cfg *config.Config) WecomSyncService {
	wecomClient := wecom.NewWeComClient(cfg.WeCom.CorpID, cfg.WeCom.ContactsSecret)
	return &syncService{
		db:          db,
		userDao:     userDao,
		wecomClient: wecomClient,
	}
}

// SyncUser 同步用户到企业微信
func (s *syncService) SyncUser(c *gin.Context, id uint, agencyID uint) (string, error) {
	// 1. 获取用户信息
	user, err := s.userDao.GetUserWithRoles(c, id)
	if err != nil {
		return "", err
	}
	if user.Phone == nil || *user.Phone == "" {
		return "", appError.NewErr("用户手机号不存在不能同步")
	}

	// 2. 获取用户所属部门
	var depts []Department
	depts, err = s.getUserDepartments(c, user)

	if err != nil {
		return "", err
	}

	// 3. 同步部门（切片引用传递）
	b := false
	if os.Getenv("GIN_MODE") != "release" {
		b = true
	}
	if err := s.syncDepartments(c, depts, b); err != nil {
		return "", err
	}

	log.Info("depts", zap.String("depts", fmt.Sprintf("%v", depts)))
	if len(depts) == 0 {
		return "", appError.NewErr("用户部门不存在")
	}
	// 获取用户部门ID
	var userDepts, deptLeader []int
	var userMain int
	for _, dept := range depts {
		if dept.Belong {
			if userMain == 0 {
				userMain = int(dept.QwPartyid)
			}
			userDepts = append(userDepts, int(dept.QwPartyid))
			deptLeader = append(deptLeader, cast.ToInt(dept.IsLeader))
		}
	}

	// 4. 同步用户
	log.Info("userDeptID", zap.String("userDeptID", fmt.Sprintf("%d", userDepts)))
	if len(userDepts) == 0 {
		return "", appError.NewErr("用户部门不存在")
	}

	qwUserID := fmt.Sprintf("yx_%d", user.ID)
	var existWecom bool
	if user.QWUserID != nil {
		qwUserID = *user.QWUserID
		existWecom = true
	}
	// 转换角色为字符串数组
	var roles []string
	for _, r := range user.Roles {
		roles = append(roles, r.Slug)
	}

	// 处理 position
	var position string
	if slices.Contains(roles, consts.RoleEndpoint) {
		position = consts.QWPosEndpoint
	} else if slices.Contains(roles, consts.RoleSalesclerk) {
		position = consts.QWPosSalesclerk
	}

	client := s.wecomClient
	if existWecom {
		// 更新用户
		err = client.UpdateUser(&wecom.UserReq{
			UserID:         qwUserID,
			Name:           user.Name,
			Mobile:         *user.Phone,
			Department:     userDepts,
			Position:       position,
			IsLeaderInDept: deptLeader,
		})
	} else {
		// 创建用户
		err = client.CreateUser(&wecom.UserReq{
			UserID:         qwUserID,
			Name:           user.Name,
			Mobile:         *user.Phone,
			Department:     userDepts,
			Position:       position,
			IsLeaderInDept: deptLeader,
			ToInvite:       true,
		})
	}

	if err != nil {
		return "", appError.NewErr(err.Error())
	}
	return qwUserID, nil
}

// AddUserTag 用户标签同步
func (s *syncService) AddUserTag(c *gin.Context, tag int, QWUserIDs []string) error {
	return s.wecomClient.AddTagUsers(tag, QWUserIDs, []int{})
}

// DeleteUserTag 用户标签删除
func (s *syncService) DeleteUserTag(c *gin.Context, tag int, QWUserIDs []string) error {
	_, err := s.wecomClient.DeleteTagUsers(tag, QWUserIDs, []int{})
	return err
}

// getUserDepartments 获取用户部门层级结构
func (s *syncService) getUserDepartments(c *gin.Context, user *model.AdminUsers) ([]Department, error) {
	var depts []Department

	if user == nil {
		return nil, appError.NewErr("用户信息不存在")
	}

	// 根据用户角色获取部门
	isAgencyUser, isTopAgency, isSecondAgency, isAgencyAssistant, isSecondAgencyAssistant := false, false, false, false, false
	isEndpointUser, isRoleEndpoint := false, false

	// 角色判断
	for _, role := range user.Roles {
		switch role.Slug {
		case consts.RoleTopAgency:
			isTopAgency = true
			isAgencyUser = true
		case consts.RoleSecondAgency:
			isSecondAgency = true
			isAgencyUser = true
		case consts.RoleTopAgencyAssistant:
			isAgencyUser = true
			isAgencyAssistant = true
		case consts.RoleSecondAgencyAssistant:
			isAgencyUser = true
			isSecondAgencyAssistant = true
		case consts.RoleEndpoint:
			isEndpointUser = true
			isRoleEndpoint = true
		case consts.RoleSalesclerk:
			isEndpointUser = true
		}
	}

	// 处理终端用户
	if isEndpointUser {
		endpoint, err := s.getEndpointDepartment(c, user.ID, isRoleEndpoint)
		if err != nil {
			return nil, err
		}
		depts = append(depts, endpoint)

		agencies, err := s.getAgencyDepartmentsForEndpoint(c, endpoint.ID)
		if err != nil {
			return nil, err
		}

		if isAgencyUser {
			s.processAgencyDepartments(agencies, isTopAgency, isSecondAgency, isAgencyAssistant, isSecondAgencyAssistant)
		}
		depts = append(depts, agencies...)
	}

	// 处理经销商用户
	if isAgencyUser && len(depts) == 0 {
		agencies, err := s.getAgencyDepartmentsForUser(c, user.ID)
		if err != nil {
			return nil, err
		}

		s.processAgencyDepartments(agencies, isTopAgency, isSecondAgency, isAgencyAssistant, isSecondAgencyAssistant)
		depts = append(depts, agencies...)
	}

	// 处理资源组用户
	if len(depts) == 0 {
		resourceGroup, err := s.getResourceGroupDepartment(c, user)
		if err != nil {
			return nil, err
		}
		if resourceGroup != nil {
			depts = append(depts, *resourceGroup)
		}
	}

	if len(depts) == 0 {
		return nil, appError.NewErr("未找到相关部门信息")
	}

	sort.Sort(Departments(depts))
	return depts, nil
}

// 获取终端部门信息
func (s *syncService) getEndpointDepartment(c *gin.Context, userID uint, isRoleEndpoint bool) (Department, error) {
	var endpoint Department
	err := s.db.WithContext(c).Table("endpoint as e").
		Select([]string{
			"e.id",
			"e.name",
			"CASE WHEN e.second_agency = 0 THEN e.top_agency ELSE e.second_agency END as parent_id",
			"3 as level",
			"e.qw_partyid",
			"e.top_agency",
			"e.second_agency",
			"'endpoint' as type",
			"true as main",
			"true as belong",
		}).
		Joins("left join user_endpoint ue ON e.id = ue.endpoint").
		Where("ue.uid = ?", userID).
		Where("e.status = 1").
		Scan(&endpoint).Error
	if err != nil {
		return Department{}, err
	}
	if isRoleEndpoint {
		endpoint.IsLeader = true
	}
	return endpoint, nil
}

// 获取终端相关的经销商部门
func (s *syncService) getAgencyDepartmentsForEndpoint(c *gin.Context, endpointID uint) ([]Department, error) {
	var agencies []Department
	err := s.db.WithContext(c).Table("agency as a").
		Select([]string{"a.id", "a.name", "a.pid as parent_id", "a.level", "a.qw_partyid", "'agency' as type"}).
		Joins("left join endpoint e ON a.id = e.top_agency OR a.id = e.second_agency").
		Where("e.id = ?", endpointID).
		Where("a.deleted_at IS NULL").
		Where("e.status = 1").
		Order("a.level").
		Scan(&agencies).Error
	return agencies, err
}

// 获取用户相关的经销商部门
func (s *syncService) getAgencyDepartmentsForUser(c *gin.Context, userID uint) ([]Department, error) {
	var agencies []Department
	err := s.db.WithContext(c).Table("agency as a").
		Select([]string{"a.id", "a.name", "a.pid as parent_id", "a.level", "a.qw_partyid", "'agency' as type"}).
		Joins("left join user_agency ua ON a.id = ua.top_agency OR a.id = ua.second_agency").
		Where("ua.uid = ?", userID).
		Where("a.deleted_at IS NULL").
		Order("a.level").
		Scan(&agencies).Error
	return agencies, err
}

// 处理经销商部门信息
func (s *syncService) processAgencyDepartments(agencies []Department, isTopAgency, isSecondAgency, isAgencyAssistant, isSecondAgencyAssistant bool) {
	for i, agency := range agencies {
		// 判断是否为主要代理商
		isMainAgency := (isTopAgency && agency.Level == 1) ||
			(isSecondAgency && agency.Level == 2) ||
			(isAgencyAssistant && agency.Level == 1) ||
			(isSecondAgencyAssistant && agency.Level == 2)

		if isMainAgency {
			agencies[i].Main = true
			agencies[i].Belong = true

			// 只有正式代理商（非助手）才是部门负责人
			if (isTopAgency && agency.Level == 1) || (isSecondAgency && agency.Level == 2) {
				agencies[i].IsLeader = true
			}
		}
	}
}

// 获取资源组部门信息
func (s *syncService) getResourceGroupDepartment(c *gin.Context, user *model.AdminUsers) (*Department, error) {
	userGroup, err := s.userDao.GetUserGroups(c, user)
	if err != nil {
		return nil, err
	}
	if userGroup == nil || userGroup.ResourceId == 0 {
		return nil, nil
	}

	var resourceGroup Department
	err = s.db.WithContext(c).Table("resource_groups as rg").
		Select([]string{"rg.qw_partyid", "true as main", "true as belong"}).
		Where("rg.id = ?", userGroup.ResourceId).
		Where("rg.deleted_at IS NULL").
		Where("rg.qw_partyid > 0").
		Scan(&resourceGroup).Error

	if errors.Is(err, gorm.ErrRecordNotFound) {
		return nil, appError.NewErr("所在资源还未同步企微")
	}
	if err != nil {
		return nil, err
	}

	return &resourceGroup, nil
}

func (s *syncService) getAgencyDepartments(c *gin.Context, agencyID uint) ([]Department, error) {
	var depts []Department

	var agencies []Department
	err := s.db.WithContext(c).Table("agency as a").
		Select([]string{"a.id", "a.name", "a.pid as parent_id", "a.level", "a.qw_partyid", "'agency' as type"}).
		Joins("left join agency a2 ON a.id = a2.id OR a.id = a2.pid").
		Where("a.id = ?", agencyID).
		Where("a.deleted_at IS NULL").
		Where("a2.deleted_at IS NULL").
		Order("a.level").
		Scan(&agencies).Error
	if err != nil {
		return nil, err
	}
	depts = append(depts, agencies...)

	if len(depts) == 0 {
		return nil, appError.NewErr("未找到相关部门信息")
	}
	sort.Sort(Departments(depts))
	return depts, nil
}

// syncDepartments 同步部门到企业微信
func (s *syncService) syncDepartments(c *gin.Context, depts []Department, update bool) error {
	client := s.wecomClient

	for i, dept := range depts {
		if dept.Level == 0 {
			continue
		}
		var qwID int
		var parentID int

		// 计算企业微信部门ID
		if dept.Level == 1 {
			qwID = QWDeptAgencyMin + int(dept.ID)
			//一级经销商根据资源组的部门ID来同步
			err := s.db.WithContext(c).Model(&model.Agency{}).
				Joins("left join resource_groups rg ON rg.id = agency.resource_id").
				Where("agency.id =?", dept.ID).
				Where("rg.deleted_at IS NULL").
				Select("rg.qw_partyid").
				First(&parentID).Error
			if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
				return err
			}
			if parentID == 0 {
				parentID = 1
			}
		} else if dept.Level == 2 {
			qwID = QWDeptSubagencyMin + int(dept.ID)
			parentID = QWDeptAgencyMin + int(dept.ParentID)
		} else if dept.Level == 3 {
			qwID = QWDeptEndpointMin + int(dept.ID)
			if i > 0 {
				parentID = int(depts[i-1].QwPartyid)
			}
		}

		if dept.QwPartyid > 0 {
			qwID = int(dept.QwPartyid)
		}

		// 同步到企业微信
		var err error
		if dept.QwPartyid > 0 {
			if !update {
				continue
			}
			// 更新部门
			err = client.UpdateDepartment(qwID, dept.Name, parentID)
			var apiErr *wecom.WeChatAPIError
			if errors.As(err, &apiErr) {
				if apiErr.ErrCode == wecom.ErrCodeDepartmentNotFound {
					_, err = client.CreateDepartment(dept.Name, parentID, qwID)
				}
			}
		} else {
			// 创建部门
			_, err = client.CreateDepartment(dept.Name, parentID, qwID)
		}

		if err != nil {
			return err
		}

		// 更新数据库中的企业微信部门ID
		if dept.QwPartyid != uint(qwID) {
			if dept.Type == "endpoint" {
				err = s.db.WithContext(c).Model(&model.Endpoint{}).
					Where("id =?", dept.ID).
					Update("qw_partyid", qwID).Error
			} else {
				err = s.db.WithContext(c).Model(&model.Agency{}).
					Where("id =?", dept.ID).
					Update("qw_partyid", qwID).Error
			}
			if err != nil {
				return err
			}
		}

		depts[i].QwPartyid = uint(qwID)
	}

	return nil
}

func (s *syncService) SyncResourceGroupDepartments(c *gin.Context, name string, parentID, qwID int) error {
	client := s.wecomClient
	_, err := client.CreateDepartment(name, parentID, qwID)
	return err
}
