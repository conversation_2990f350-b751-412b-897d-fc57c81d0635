package reimbursement

import (
	"encoding/json"
	"errors"
	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
	api "marketing/internal/api/reimbursement"
	"marketing/internal/model"
	"marketing/internal/pkg/utils"
	"strings"
	"time"
)

// GetPromotionalProductsQuickPickStat 获取促销产品快捷键统计
func (r *repo) GetPromotionalProductsQuickPickStat(c *gin.Context, req *api.PromotionalProductsQuickPickStatReq) (*api.PromotionalProductsQuickPickStatResp, error) {
	var result api.PromotionalProductsQuickPickStatResp

	// Build the base query
	var query *gorm.DB

	if req.ReimbursementStatus != 0 {
		// Query with reimbursement_apply_order_summary join
		query = r.db.WithContext(c).Table("reimbursement_promotional_products_list rpp").
			Select(`count(*) as total_count,
				SUM(CASE WHEN rpp.status = 0 AND rpp.voucher_audit_rollback = 0 THEN 1 ELSE 0 END) AS pending_audit_count,
				SUM(CASE WHEN rpp.status = 1 THEN 1 ELSE 0 END) AS first_approved_count,
				SUM(CASE WHEN rpp.status = 2 THEN 1 ELSE 0 END) AS final_approved_count,
				SUM(CASE WHEN rpp.status = 3 THEN 1 ELSE 0 END) AS completed_count,
				SUM(CASE WHEN rpp.status = -1 THEN 1 ELSE 0 END) AS rejected_count,
				SUM(CASE WHEN rpp.status = -3 THEN 1 ELSE 0 END) AS failed_count,
				SUM(CASE WHEN rpp.status = -100 THEN 1 ELSE 0 END) AS cancelled_count`).
			Joins("LEFT JOIN reimbursement_apply_order_summary raos ON rpp.id = raos.apply_order_id").
			Where("raos.status = ? AND raos.apply_order_type = ? AND rpp.policy_id = ?",
				req.ReimbursementStatus, "promotional_products", req.PolicyID)
	} else {
		// Query without join
		query = r.db.WithContext(c).Table("reimbursement_promotional_products_list rpp").
			Select(`count(*) as total_count,
				SUM(CASE WHEN rpp.status = 0 AND rpp.voucher_audit_rollback = 0 THEN 1 ELSE 0 END) AS pending_audit_count,
				SUM(CASE WHEN rpp.status = 1 THEN 1 ELSE 0 END) AS first_approved_count,
				SUM(CASE WHEN rpp.status = 2 THEN 1 ELSE 0 END) AS final_approved_count,
				SUM(CASE WHEN rpp.status = 3 THEN 1 ELSE 0 END) AS completed_count,
				SUM(CASE WHEN rpp.status = -1 THEN 1 ELSE 0 END) AS rejected_count,
				SUM(CASE WHEN rpp.status = -3 THEN 1 ELSE 0 END) AS failed_count,
				SUM(CASE WHEN rpp.status = -100 THEN 1 ELSE 0 END) AS cancelled_count`).
			Where("rpp.policy_id = ?", req.PolicyID)
	}

	// Add optional filters
	if req.OrderID != 0 {
		query = query.Where("rpp.id = ?", req.OrderID)
	}

	if req.TopAgency != 0 {
		query = query.Where("rpp.top_agency = ?", req.TopAgency)
	}

	if req.StartTime != "" && req.EndTime != "" {
		query = query.Where("rpp.created_at BETWEEN ? AND ?", req.StartTime, req.EndTime)
	}

	if req.Status != 0 {
		query = query.Where("rpp.status = ?", req.Status)
		if req.Status == 0 {
			query = query.Where("rpp.voucher_audit_rollback = 0")
		}
	}

	if req.CompanyID != 0 {
		query = query.Where("rpp.company_id = ?", req.CompanyID)
	}

	if req.MaterialReturnStatus != 0 {
		query = query.Where("rpp.material_return_status = ?", req.MaterialReturnStatus)
	}

	if req.ExpressComeSn != "" {
		query = query.Where("rpp.express_come_sn = ?", req.ExpressComeSn)
	}

	if req.CompletionStatus != 0 {
		query = query.Where("rpp.completion_status = ?", req.CompletionStatus)
	}

	// Execute the query
	var queryResult struct {
		TotalCount         int `json:"total_count"`
		PendingAuditCount  int `json:"pending_audit_count"`
		FirstApprovedCount int `json:"first_approved_count"`
		FinalApprovedCount int `json:"final_approved_count"`
		CompletedCount     int `json:"completed_count"`
		RejectedCount      int `json:"rejected_count"`
		FailedCount        int `json:"failed_count"`
		CancelledCount     int `json:"cancelled_count"`
	}

	err := query.Scan(&queryResult).Error
	if err != nil {
		return nil, err
	}

	// Map the result to response struct
	result.TotalCount = queryResult.TotalCount
	result.PendingAuditCount = queryResult.PendingAuditCount
	result.FirstApprovedCount = queryResult.FirstApprovedCount
	result.FinalApprovedCount = queryResult.FinalApprovedCount
	result.CompletedCount = queryResult.CompletedCount
	result.RejectedCount = queryResult.RejectedCount
	result.FailedCount = queryResult.FailedCount
	result.CancelledCount = queryResult.CancelledCount

	return &result, nil
}

// GetPromotionalProductsDetail 获取推广产品详情
func (r *repo) GetPromotionalProductsDetail(c *gin.Context, req *api.OrderReq) (*api.PromotionalProductsDetailResp, error) {
	orderID := req.ID

	var detail *api.PromotionalProductsDetailResp

	detail, err := r.getBackstagePromotionalProductsDetail(orderID)

	if err != nil {
		return nil, err
	}

	if detail == nil {
		return nil, nil
	}

	// 获取产品信息
	products, err := r.GetReimbursementProducts(c, orderID)
	if err != nil {
		return nil, err
	}
	detail.Products = products

	return detail, nil
}

// getBackstagePromotionalProductsDetail 获取后台推广产品详情
func (r *repo) getBackstagePromotionalProductsDetail(orderID int) (*api.PromotionalProductsDetailResp, error) {
	var result api.ReimbursementPromotionalProduct

	err := r.db.Table("reimbursement_promotional_products_list rpp").
		Select(`rpp.id, rpp.sn, rpp.uid, rpp.top_agency, rpp.second_agency, rpp.company_id, rpp.amount,
			rpp.company, rpp.name AS contact_name, rpp.phone AS contact_phone,
			rep.region_name AS province_name, rc.region_name AS city_name, rd.region_name AS district_name,
			rpp.address, rpp.status, rpp.actual_amount, rpp.created_at, rpp.voucher_audit_man,
			rpp.voucher_audit_time, rpp.voucher_audit_remark, rpp.voucher_audit_rollback,
			rpp.data_audit_man, rpp.data_verify_time, rpp.data_audit_remark, rpp.data_audit_rollback, rpp.policy_id,
			rp.name AS policy_name, rp.start_time as policy_start_time, rp.end_time AS policy_end_time, rp.archive,
			au.phone, a.name as agency_name, rpp.express_go_sn, rpp.express_go_com, rpp.express_go_time,
			rpp.express_come_sn, rpp.express_come_com, rpp.express_come_time`).
		Joins("LEFT JOIN reimbursement_policy rp ON rpp.policy_id = rp.id").
		Joins("LEFT JOIN admin_users au ON rpp.uid = au.id").
		Joins("LEFT JOIN agency a ON rpp.top_agency = a.id").
		Joins("LEFT JOIN region rep ON rpp.province = rep.region_id").
		Joins("LEFT JOIN region rc ON rpp.city = rc.region_id").
		Joins("LEFT JOIN region rd ON rpp.district = rd.region_id").
		Where("rpp.id = ?", orderID).
		Scan(&result).Error
	if err != nil {
		return nil, err
	}

	detail := &api.PromotionalProductsDetailResp{
		ID:                   result.ID,
		SN:                   result.SN,
		UID:                  result.UID,
		TopAgency:            result.TopAgency,
		SecondAgency:         result.SecondAgency,
		CompanyID:            result.CompanyID,
		Amount:               result.Amount,
		Company:              result.Company,
		ContactName:          result.ContactName,
		ContactPhone:         result.ContactPhone,
		ProvinceName:         result.ProvinceName,
		CityName:             result.CityName,
		DistrictName:         result.DistrictName,
		Address:              result.Address,
		Status:               result.Status,
		ActualAmount:         result.ActualAmount,
		CreatedAt:            result.CreatedAt.Format("2006-01-02 15:04:05"),
		VoucherAuditMan:      result.VoucherAuditMan,
		VoucherAuditRemark:   result.VoucherAuditRemark,
		VoucherAuditRollback: result.VoucherAuditRollback,
		DataAuditMan:         result.DataAuditMan,
		DataAuditRemark:      result.DataAuditRemark,
		DataAuditRollback:    result.DataAuditRollback,
		PolicyID:             result.PolicyID,
		PolicyName:           result.PolicyName,
		PolicyStartTime:      result.PolicyStartTime.Format("2006-01-02"),
		PolicyEndTime:        result.PolicyEndTime.Format("2006-01-02"),
		Archive:              result.Archive,
		Phone:                result.Phone,
		AgencyName:           result.AgencyName,
		ExpressGoSN:          result.ExpressGoSN,
		ExpressGoCom:         result.ExpressGoCom,
		ExpressComeSN:        result.ExpressComeSN,
		ExpressComeCom:       result.ExpressComeCom,
	}

	if result.VoucherAuditTime != nil {
		timeStr := result.VoucherAuditTime.Format("2006-01-02 15:04:05")
		detail.VoucherAuditTime = &timeStr
	}
	if result.DataVerifyTime != nil {
		timeStr := result.DataVerifyTime.Format("2006-01-02 15:04:05")
		detail.DataVerifyTime = &timeStr
	}
	if result.ExpressGoTime != nil {
		timeStr := result.ExpressGoTime.Format("2006-01-02 15:04:05")
		detail.ExpressGoTime = &timeStr
	}
	if result.ExpressComeTime != nil {
		timeStr := result.ExpressComeTime.Format("2006-01-02 15:04:05")
		detail.ExpressComeTime = &timeStr
	}

	return detail, nil
}

// GetReimbursementProducts 获取报销产品信息
func (r *repo) GetReimbursementProducts(c *gin.Context, orderID int) ([]api.PromotionalProductDetail, error) {
	var products []api.PromotionalProductDetail

	sql := r.db.WithContext(c).Table("reimbursement_promotional_products_relation AS rppr").
		Select("rppr.id, rpp.id as product_id, rpp.preview, rpp.name, rppr.norm, rpp.unit, rppr.price_type, " +
			"rpp.include_tax_price, rpp.exclude_tax_price, rpp.reimbursement_price, rppr.quantity, rppr.price, " +
			"rppr.price*rppr.norm*rppr.quantity AS total_price, rpp.include_tax_account_info, rpp.exclude_tax_account_info, rpp.communication_letter").
		Joins("RIGHT JOIN reimbursement_promotional_products rpp ON rpp.id = rppr.product_id")

	var results []struct {
		ID                    int     `db:"id"`
		ProductID             int     `db:"product_id"`
		Preview               string  `db:"preview"`
		Name                  string  `db:"name"`
		Norm                  string  `db:"norm"`
		Unit                  string  `db:"unit"`
		PriceType             string  `db:"price_type"`
		IncludeTaxPrice       float64 `db:"include_tax_price"`
		ExcludeTaxPrice       float64 `db:"exclude_tax_price"`
		ReimbursementPrice    float64 `db:"reimbursement_price"`
		Quantity              int     `db:"quantity"`
		Price                 float64 `db:"price"`
		TotalPrice            float64 `db:"total_price"`
		IncludeTaxAccountInfo string  `db:"include_tax_account_info"`
		ExcludeTaxAccountInfo string  `db:"exclude_tax_account_info"`
		CommunicationLetter   string  `db:"communication_letter"`
	}

	err := sql.Where("rppr.reimbursement_id = ?", orderID).Scan(&results).Error
	if err != nil {
		return nil, err
	}

	for _, result := range results {
		product := api.PromotionalProductDetail{
			ID:                 result.ID,
			ProductID:          result.ProductID,
			Name:               result.Name,
			Norm:               result.Norm,
			Unit:               result.Unit,
			PriceType:          result.PriceType,
			IncludeTaxPrice:    result.IncludeTaxPrice,
			ExcludeTaxPrice:    result.ExcludeTaxPrice,
			ReimbursementPrice: result.ReimbursementPrice,
			Quantity:           result.Quantity,
			Price:              result.Price,
			TotalPrice:         result.TotalPrice,
		}

		// 解析JSON字符串为FileMetadata数组
		product.IncludeTaxAccountInfo = r.loadFiles(result.IncludeTaxAccountInfo)
		product.ExcludeTaxAccountInfo = r.loadFiles(result.ExcludeTaxAccountInfo)
		product.CommunicationLetter = r.loadFiles(result.CommunicationLetter)
		product.Preview = r.loadFiles(result.Preview)

		products = append(products, product)
	}

	return products, nil
}

// loadFiles 解析JSON字符串为FileMetadata数组
func (r *repo) loadFiles(jsonStr string) []api.FileMetadata {
	if jsonStr == "" {
		return []api.FileMetadata{}
	}

	var files []api.FileMetadata
	err := json.Unmarshal([]byte(jsonStr), &files)
	if err != nil {
		// 如果解析失败，返回空数组
		return []api.FileMetadata{}
	}

	// 为每个文件URL添加OSS域名前缀
	for i := range files {
		if files[i].URL != "" && !strings.HasPrefix(files[i].URL, "http") {
			files[i].URL = utils.AddPrefix(files[i].URL)
		}
	}

	return files
}

func (r *repo) GetPromotionalProductsOrder(c *gin.Context, orderID int, agency map[string]int) (*model.ReimbursementPromotionalProductsList, error) {
	var product model.ReimbursementPromotionalProductsList

	query := r.db.WithContext(c).Table("reimbursement_promotional_products_list").Where("id = ?", orderID)
	if agency != nil {
		query = query.Where("top_agency = ? AND second_agency = ?", agency["top_agency"], agency["second_agency"])
	}

	err := query.First(&product).Error
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, nil
		}
		return nil, err
	}

	return &product, nil
}

// InvalidPromotionalProducts 促销品作废
func (r *repo) InvalidPromotionalProducts(c *gin.Context, orderID int) (bool, error) {
	// 更新状态为-100（作废）
	err := r.db.WithContext(c).Model(&model.ReimbursementPromotionalProductsList{}).
		Where("id = ?", orderID).
		Update("status", -100).Error

	if err != nil {
		return false, err
	}

	return true, nil
}

// ReviewVoucherPromotionalProducts 促销品凭证审核
func (r *repo) ReviewVoucherPromotionalProducts(c *gin.Context, req *api.ReimbursementAuditReq) error {
	now := time.Now()

	return r.db.WithContext(c).Transaction(func(tx *gorm.DB) error {
		// 1. 更新促销品申请记录状态
		updates := map[string]interface{}{
			"voucher_audit_man":    req.Uid,
			"voucher_audit_remark": req.Remark,
			"voucher_audit_time":   now,
			"updated_at":           now,
		}

		if req.Status == -1 {
			// 审核不通过，回滚到申请审核通过状态
			updates["status"] = 0
			updates["voucher_audit_rollback"] = 1
		} else {
			// 审核通过
			updates["status"] = 1
			updates["voucher_audit_rollback"] = 0
		}

		err := tx.Model(&model.ReimbursementPromotionalProductsList{}).
			Where("id = ?", req.ID).
			Updates(updates).Error
		if err != nil {
			return err
		}

		return nil
	})
}

// ReviewDataPromotionalProducts 促销品收货单审批
func (r *repo) ReviewDataPromotionalProducts(c *gin.Context, req *api.ReimbursementAuditReq, order *model.ReimbursementPromotionalProductsList) error {
	return r.db.WithContext(c).Transaction(func(tx *gorm.DB) error {
		now := time.Now()

		// 1. 更新促销品申请记录状态
		updates := map[string]interface{}{
			"data_audit_man":    req.Uid,
			"data_audit_remark": req.Remark,
			"data_verify_time":  now,
			"updated_at":        now,
		}

		if req.Status == -1 {
			// 审核不通过，回滚到步骤3
			updates["status"] = 1
			updates["data_audit_rollback"] = 1
		} else {
			// 审核通过
			updates["status"] = 3
			updates["data_audit_rollback"] = 0
		}

		err := tx.Model(&model.ReimbursementPromotionalProductsList{}).
			Where("id = ?", req.ID).
			Updates(updates).Error
		if err != nil {
			return err
		}

		// 2. 如果审核通过，创建汇总记录
		if req.Status != -1 {
			summary := &model.ReimbursementApplyOrderSummary{
				ApplyOrderID:             req.ID,
				SN:                       order.SN,
				UID:                      order.UID,
				TopAgency:                order.TopAgency,
				SecondAgency:             0,
				CompanyID:                order.CompanyID,
				Code:                     order.Code,
				Company:                  order.Company,
				PolicyID:                 order.PolicyID,
				Amount:                   order.Amount,
				ReimbursementApplyAmount: order.ReimbursementApplyAmount,
				QuantityTotal:            order.QuantityTotal,
				Status:                   0,
				ApplyOrderType:           "promotional_products",
				ReimbursementType:        order.ReimbursementType,
				CreatedAt:                order.CreatedAt,
				UpdatedAt:                &now,
			}

			if err := tx.Create(summary).Error; err != nil {
				return err
			}
		}

		return nil
	})
}

// AddExpressInfo 添加快递信息
func (r *repo) AddExpressInfo(c *gin.Context, req *api.AddExpressInfoReq) error {
	// 更新快递信息
	err := r.db.WithContext(c).Model(&model.ReimbursementPromotionalProductsList{}).
		Where("id = ?", req.ID).
		Updates(map[string]interface{}{
			"express_go_sn":   req.ExpressSN,   // 快递单号
			"express_go_com":  req.ExpressCom,  // 快递公司
			"express_go_time": req.ExpressTime, // 发货时间
			"updated_at":      time.Now(),      // 更新时间
		}).Error

	return err
}

// PromotionalProductsSplit 促销品订单拆分
func (r *repo) PromotionalProductsSplit(c *gin.Context, req *api.PromotionalProductsSplitReq) error {
	return r.db.Transaction(func(tx *gorm.DB) error {
		now := time.Now()

		// 1. 更新原订单状态为-3
		err := tx.Table("reimbursement_apply_order_summary").
			Where("id = ?", req.OrderID).
			Updates(map[string]interface{}{
				"status":     -3,
				"updated_at": now,
			}).Error
		if err != nil {
			return err
		}

		// 2. 获取原订单信息用于复制
		var order struct {
			ApplyOrderID         int64  `gorm:"column:apply_order_id"`
			UID                  int64  `gorm:"column:uid"`
			TopAgency            string `gorm:"column:top_agency"`
			SecondAgency         string `gorm:"column:second_agency"`
			CompanyID            string `gorm:"column:company_id"`
			Code                 string `gorm:"column:code"`
			Company              string `gorm:"column:company"`
			PolicyID             int64  `gorm:"column:policy_id"`
			ApplyOrderType       string `gorm:"column:apply_order_type"`
			ReimbursementType    int    `gorm:"column:reimbursement_type"`
			MaterialReturnStatus int    `gorm:"column:material_return_status"`
		}
		err = tx.Table("reimbursement_apply_order_summary").
			Where("id = ?", req.OrderID).
			First(&order).Error
		if err != nil {
			return err
		}

		// 3. 插入拆分后的新订单
		for _, item := range req.OrderSplitInfo {
			sn := utils.GenerateUniqueSN() // 需要实现生成唯一编号的函数
			newOrder := map[string]interface{}{
				"apply_order_id":         order.ApplyOrderID,
				"uid":                    order.UID,
				"sn":                     sn,
				"top_agency":             order.TopAgency,
				"second_agency":          order.SecondAgency,
				"company_id":             order.CompanyID,
				"code":                   order.Code,
				"company":                order.Company,
				"policy_id":              order.PolicyID,
				"amount":                 item.Amount,
				"quantity_total":         item.Quantity,
				"apply_order_type":       order.ApplyOrderType,
				"reimbursement_type":     order.ReimbursementType,
				"split_type":             1,
				"order_id":               req.OrderID,
				"material_return_status": order.MaterialReturnStatus,
				"created_at":             now,
				"updated_at":             now,
			}

			err = tx.Table("reimbursement_apply_order_summary").
				Create(newOrder).Error
			if err != nil {
				return err
			}
		}

		return nil
	})
}

func (r *repo) GetPendingPromotionalProducts(c *gin.Context, companyID int, topAgency uint) ([]api.PendingTask, error) {
	var results []api.PendingTask

	err := r.db.WithContext(c).
		Table("reimbursement_promotional_products_list rppl").
		Select(`rp.id AS policy_id, rp.name AS policy_name, rp.policy_type, 
                rppl.id, rppl.sn, rppl.status, rppl.created_at,
                rppl.amount/2 as amount, rppl.company,
                rppl.voucher_audit_rollback, rppl.data_audit_rollback, ? as type`, 1).
		Joins("LEFT JOIN reimbursement_policy rp ON rppl.policy_id = rp.id").
		Where(r.db.Where("rppl.voucher_audit_rollback = ? AND rppl.status = ?", 1, 0).
			Or("rppl.status = ?", 1)).
		Where("rppl.top_agency = ? AND rppl.company_id = ?", topAgency, companyID).
		Scan(&results).Error

	if err != nil {
		return nil, err
	}

	return results, nil
}

func (r *repo) GetPromotionalProductsOrders(c *gin.Context, req *api.ClientOrdersReq) ([]*api.ClientOrdersResp, error) {
	offset := (req.Page - 1) * req.PageSize

	// 状态转换
	dbStatus := 0
	switch req.Status {
	case 1:
		dbStatus = 0
	case 2:
		dbStatus = 1
	case 3:
		dbStatus = 2
	}

	var results []*api.ClientOrdersResp
	query := r.db.WithContext(c).Table("reimbursement_promotional_products_list rppl").
		Select(`
            rp.id as policy_id,
            rp.name as policy_name,
            rp.policy_type,
            rppl.id,
            rppl.sn,
            rppl.status,
            rppl.created_at,
            IF(rppl.reimbursement_apply_amount = 0, rppl.amount/2, rppl.reimbursement_apply_amount) as amount,
            rppl.company,
            ? as type`, 1).
		Joins("LEFT JOIN reimbursement_policy rp ON rppl.policy_id = rp.id").
		Where("rppl.top_agency = ? AND rppl.company_id = ? AND rppl.policy_id = ? AND rppl.status = ?",
			req.TopAgency, req.CompanyID, req.PolicyID, dbStatus)

	if req.StartTime != "" && req.EndTime != "" {
		query = query.Where("rppl.created_at BETWEEN ? AND ?", req.StartTime, req.EndTime)
	}

	err := query.Order("rppl.created_at desc").
		Offset(offset).
		Limit(req.PageSize).
		Scan(&results).Error
	if err != nil {
		return nil, err
	}

	return results, nil
}

func (r *repo) GetPromotionalProductsApplySummaryOrders(c *gin.Context, req *api.ClientOrdersReq) ([]*api.ClientOrdersResp, error) {
	offset := (req.Page - 1) * req.PageSize

	var results []*api.ClientOrdersResp
	query := r.db.WithContext(c).Table("reimbursement_apply_order_summary raos").
		Select(`
            rp.id as policy_id,
            rp.name as policy_name,
            rp.policy_type,
            raos.id,
            raos.apply_order_id,
            raos.sn,
            raos.status,
            raos.created_at,
            IF(raos.reimbursement_apply_amount = 0, raos.amount/2, raos.reimbursement_apply_amount) as amount,
            raos.company,
            raos.turn_type,
            raos.split_type,
            ? as type`, 3).
		Joins("LEFT JOIN reimbursement_policy rp ON raos.policy_id = rp.id").
		Where("(raos.status = 0 OR raos.status = -100) AND raos.top_agency = ? AND raos.company_id = ? AND raos.policy_id = ?",
			req.TopAgency, req.CompanyID, req.PolicyID)

	if req.StartTime != "" && req.EndTime != "" {
		query = query.Where("raos.created_at BETWEEN ? AND ?", req.StartTime, req.EndTime)
	}

	err := query.Order("raos.created_at desc").
		Offset(offset).
		Limit(req.PageSize).
		Scan(&results).Error
	if err != nil {
		return nil, err
	}

	return results, nil
}
