package service

import (
	"errors"
	"log"
	"marketing/internal/api/action"
	"marketing/internal/consts"
	"marketing/internal/dao"
	"marketing/internal/model"
	myErrors "marketing/internal/pkg/errors"
	"path/filepath"
	"sort"
	"strconv"
	"strings"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/gogf/gf/container/garray"
	"github.com/gogf/gf/os/gfile"
	"github.com/gogf/gf/util/guid"
	"github.com/spf13/cast"
	"github.com/xuri/excelize/v2"
	"gorm.io/gorm"
)

type PromotionService interface {
	Create(c *gin.Context, parma action.PromotionAddReq) (int64, error)
	Delete(c *gin.Context, ids []int) error
	Update(c *gin.Context, param action.PromotionUpdateReq) error
	GetList(c *gin.Context, param action.PromotionsReq) ([]map[string]any, int64, error)
	GetInfo(c *gin.Context, id string) (map[string]any, error)
	GetAgencyTips(c *gin.Context, name string) ([]map[string]any, error)
	GetEndpointTips(c *gin.Context, name string, id string) ([]map[string]any, error)
	GetModelTips(c *gin.Context, name string) ([]map[string]any, error)
	GetJoinList(c *gin.Context, id int, page int, size int, agency string, endpoint string, status string,
		model string, barcode string) ([]action.PromotionList, int64, error)
	GetJoinInfo(c *gin.Context, id string) (map[string]any, map[string]any, error)
	AddJoin(c *gin.Context, id int, barcode string) (int, error)
	DeleteJoin(c *gin.Context, ids []int) error
	SyncJoin(c *gin.Context, id int) error
	Export(c *gin.Context, id int, page int, size int, agency string, endpoint string, status string,
		model string) (string, error)
	UpdateReceipt(c *gin.Context, req action.UpdatePromotionReq) error
	GetJoinListByEndpoint(c *gin.Context, page int, size int, model string, status string, id int, barcode string) ([]action.PromotionListApp, int64, error)
	AgencyReceipt(c *gin.Context, id int, agencyID uint, receipt []string) error
	FallBack(c *gin.Context, id string) error
	GetBarcodeInfo(c *gin.Context, barcode string) (*action.BarcodeResp, error)
}
type GormPromotionService struct {
	dao    dao.PromotionDao
	common dao.CommonDao
}

func (g *GormPromotionService) FallBack(c *gin.Context, id string) error {
	return g.dao.FallBack(c, id)
}

func (g *GormPromotionService) AgencyReceipt(c *gin.Context, id int, agencyID uint, receipt []string) error {
	//1.参与名单详情，检查id是否存在
	ListDetail, _, err := g.dao.GetJoinInfo(c, strconv.Itoa(id))
	if err != nil {
		return err
	}

	if ListDetail == nil {
		return myErrors.NewErr("名单不存在")
	}
	//2.检查是否是当前用户参与
	if cast.ToUint(ListDetail["top_agency"]) != agencyID && cast.ToUint(ListDetail["second_agency"]) != agencyID {
		return myErrors.NewErr("无权操作")
	}
	//3.回执上传截止时间
	detail, err := g.dao.GetDetail(c, cast.ToInt(ListDetail["sales_promotion_id"]))
	if err != nil {
		return err
	}

	Receipt, err := time.Parse("2006-01-02", detail.ReceiptDay[:10])
	if err != nil {
		return err
	}
	if time.Now().After(Receipt) {
		return myErrors.NewErr("回执上传已经过了截止时间")
	}
	//4.上传回执(1-终端，2-代理)
	return g.dao.UploadReceipt(c, id, receipt, 2)
}

func (g *GormPromotionService) GetJoinListByEndpoint(c *gin.Context, page int, size int, model string, status string,
	id int, barcode string) ([]action.PromotionListApp, int64, error) {

	list, count, err := g.dao.GetJoinList(c, id, page, size, "", "", status, model, barcode)
	lists := make([]action.PromotionListApp, len(list))
	if err != nil {
		return nil, 0, err
	}

	//获取参与名单
	ids := make([]int, 0)
	warranty := make([]int, 0)
	for _, v := range list {
		ids = append(ids, v.ID)
		warranty = append(warranty, v.WarrantyID)
	}

	//批次查询回执图片
	receipt, err := g.dao.BatchGetReceipt(c, ids)
	if err != nil {
		return nil, 0, err
	}
	res, err := g.common.WarrantyInfo(c, warranty)
	if err != nil {
		return nil, 0, err
	}

	//回执处理
	for k, v := range list {
		for _, v1 := range receipt {
			if v.ID == v1.SalesPromotionListId {
				list[k].Receipt = v1.Receipt
			}
		}
		lists[k].List = v
	}

	//额外信息处理
	for k, v := range lists {
		for _, v1 := range res {
			if v.List.WarrantyID == v1.ID {
				lists[k].Number = v1.Number
				lists[k].StudentName = v1.StudentName
				lists[k].ActivatedAt = v1.ActivatedAtOld
			}
		}
	}
	return lists, count, nil
}

// UpdateReceipt 修改回执上传截止时间
func (g *GormPromotionService) UpdateReceipt(c *gin.Context, req action.UpdatePromotionReq) error {
	return g.dao.UpdateReceiptTime(c, req)
}

func (g *GormPromotionService) Export(c *gin.Context, id int, page int, size int, agency string, endpoint string, status string,
	model string) (string, error) {
	var (
		title = []string{
			"区域",
			"终端",
			"终端代码",
			"门店地址",
			"店长名称",
			"店长电话",
			"机型",
			"S/N码",
			"序列号",
			"客户姓名",
			"联系电话",
			"学生姓名",
			"购买时间",
			"激活时间",
			"激活手机号码",
			"购买激活小时差",
			"购买激活手机号是否一致",
			"是否已上传凭证",
		}
		titleStyle     int
		addr           string
		err            error
		ExistAgency    = garray.NewStrArray()
		ExistAgencyMap = make(map[string]int)
		sheet          = "Sheet1"
	)
	data, err := g.dao.Lists(c, id, page, size, agency, endpoint, status, model)
	if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
		return "", err
	}
	f := excelize.NewFile()
	defer func() {
		if err := f.Close(); err != nil {
			log.Printf("出错了：%v", err)
		}
	}()
	// Sheet1标题行样式
	if titleStyle, err = f.NewStyle(&excelize.Style{
		Font:      &excelize.Font{Color: "1f7f3b", Bold: true, Family: "Microsoft YaHei"},
		Fill:      excelize.Fill{Type: "pattern", Color: []string{"E6F4EA"}, Pattern: 1},
		Alignment: &excelize.Alignment{Vertical: "center"},
		Border: []excelize.Border{
			{Type: "top", Style: 2, Color: "1f7f3b"},
			{Type: "bottom", Style: 1, Color: "1f7f3b"},
			{Type: "left", Style: 1, Color: "1f7f3b"}},
	}); err != nil {
		return "", err
	}
	if err = f.SetSheetRow(sheet, "A1", &title); err != nil {
		return "", err
	}
	if err = f.SetRowHeight(sheet, 1, 30); err != nil {
		return "", err
	}
	// 设置标题行样式
	if err = f.SetCellStyle(sheet, "A1", "T1", titleStyle); err != nil {
		return "", err
	}
	// 设置列宽
	if err = f.SetColWidth(sheet, "A", "T", 20); err != nil {
		return "", err
	}

	for rk, row := range data {
		found := ExistAgency.Contains(row.AgencyName)
		if !found {
			//新建工作表
			index, err := f.NewSheet(row.AgencyName)
			if index == 0 {
				return "", err
			}
			//设置头部
			if err = f.SetSheetRow(row.AgencyName, "A1", &title); err != nil {
				return "", err
			}
			if err = f.SetRowHeight(row.AgencyName, 1, 30); err != nil {
				return "", err
			}
			// 设置标题行样式
			if err = f.SetCellStyle(row.AgencyName, "A1", "T1", titleStyle); err != nil {
				return "", err
			}
			// 设置列宽
			if err = f.SetColWidth(row.AgencyName, "A", "T", 20); err != nil {
				return "", err
			}

			//赋值
			if addr, err = excelize.JoinCellName("A", 2); err != nil {
				return "", err
			}
			isSamePhone := "否"
			if row.ActivatedPhone == row.CustomerPhone {
				isSamePhone = "是"
			}
			isReceipt := "否"
			if row.IsReceipt == 1 {
				isReceipt = "是"
			}
			var tempRow []interface{}
			tempRow = append(tempRow, row.RegionName)
			tempRow = append(tempRow, row.EndpointName)
			tempRow = append(tempRow, row.EndpointCode)
			tempRow = append(tempRow, row.Address)
			tempRow = append(tempRow, row.Manager)
			tempRow = append(tempRow, row.EndpointPhone)
			tempRow = append(tempRow, row.Model)
			tempRow = append(tempRow, row.Barcode)
			tempRow = append(tempRow, row.Number)
			tempRow = append(tempRow, row.CustomerName)
			tempRow = append(tempRow, row.CustomerPhone)
			tempRow = append(tempRow, row.StudentName)
			tempRow = append(tempRow, row.BuyDate)
			tempRow = append(tempRow, row.ActivatedAt)
			tempRow = append(tempRow, row.ActivatedPhone)
			tempRow = append(tempRow, row.HourInterval)
			tempRow = append(tempRow, isSamePhone)
			tempRow = append(tempRow, isReceipt)
			if err = f.SetSheetRow(row.AgencyName, addr, &tempRow); err != nil {
				return "", err
			}
			if err = f.SetSheetRow(sheet, "A"+strconv.Itoa(rk+2), &tempRow); err != nil {
				return "", err
			}
			ExistAgency.Append(row.AgencyName)
			ExistAgencyMap[row.AgencyName] = 2
		} else {
			if addr, err = excelize.JoinCellName("A", ExistAgencyMap[row.AgencyName]+1); err != nil {
				return "", err
			}
			var tempRow []interface{}
			isSamePhone := "否"
			if row.ActivatedPhone == row.CustomerPhone {
				isSamePhone = "是"
			}
			isReceipt := "否"
			if row.IsReceipt == 1 {
				isReceipt = "是"
			}
			tempRow = append(tempRow, row.RegionName)
			tempRow = append(tempRow, row.EndpointName)
			tempRow = append(tempRow, row.EndpointCode)
			tempRow = append(tempRow, row.Address)
			tempRow = append(tempRow, row.Manager)
			tempRow = append(tempRow, row.EndpointPhone)
			tempRow = append(tempRow, row.Model)
			tempRow = append(tempRow, row.Barcode)
			tempRow = append(tempRow, row.Number)
			tempRow = append(tempRow, row.CustomerName)
			tempRow = append(tempRow, row.CustomerPhone)
			tempRow = append(tempRow, row.StudentName)
			tempRow = append(tempRow, row.BuyDate)
			tempRow = append(tempRow, row.ActivatedAt)
			tempRow = append(tempRow, row.ActivatedPhone)
			tempRow = append(tempRow, row.HourInterval)
			tempRow = append(tempRow, isSamePhone)
			tempRow = append(tempRow, isReceipt)
			if err = f.SetSheetRow(row.AgencyName, addr, &tempRow); err != nil {
				return "", err
			}
			if err = f.SetSheetRow(sheet, "A"+strconv.Itoa(rk+2), &tempRow); err != nil {
				return "", err
			}
			ExistAgencyMap[row.AgencyName] += 1

		}
	}
	fileName := gfile.TempDir() + string(filepath.Separator) + guid.S() + ".xlsx"
	if err = f.SaveAs(fileName); err != nil {
		return "", err
	}
	return fileName, err
}

// SyncJoin 名单同步
func (g *GormPromotionService) SyncJoin(c *gin.Context, id int) error {
	//1.名单是否已经同步
	detail, err := g.dao.GetDetail(c, id)
	if err != nil {
		return err
	}
	if detail.IsSync == 1 {
		return myErrors.NewErr("名单已同步,不允许重新同步")
	}
	//2.筛序保卡数据
	list, err := g.common.SiftWarranty(c, detail)
	if err != nil {
		return err
	}
	if len(list) == 0 {
		return myErrors.NewErr("没有符合条件的名单")
	}
	//3.注册电话与激活号码是否一致
	var ids []int
	for _, v := range list {
		ids = append(ids, v.ActivatedID)
	}
	res, _ := g.common.GetAcDevices(c, ids)
	result := make(map[int]string, len(res))
	for _, v := range res {
		result[v.ID] = v.Origin
	}
	join := make([]action.SiftWarranty, 0, len(list))
	for _, v := range list {
		//要求一致
		if detail.IsSamePhone == 1 {
			if v.ActivatedID == 0 || result[v.ActivatedID] != v.CustomerPhone {
				continue
			}
		}
		if v.ActivatedID == 0 || result[v.ActivatedID] != v.CustomerPhone {
			v.IsSamePhone = 0
		} else {
			v.IsSamePhone = 1
		}
		v.ActivatedPhone = result[v.ActivatedID]
		join = append(join, v)
	}
	if len(join) == 0 {
		return myErrors.NewErr("没有符合条件的名单")
	}
	err = g.dao.SyncJoin(c, join)
	if err != nil {
		return err
	}
	return g.dao.Update(c, model.SalesPromotion{
		ID:     int64(id),
		IsSync: 1,
	})
}

func (g *GormPromotionService) DeleteJoin(c *gin.Context, ids []int) error {
	return g.dao.DeleteJoin(c, ids)
}

func (g *GormPromotionService) AddJoin(c *gin.Context, id int, barcode string) (int, error) {
	exist, err := g.dao.IsExist(c, id, barcode)
	if exist != nil && exist.ID > 0 {
		//判断保卡是否已经删除
		warrantyDeleted := g.dao.IsWarrantyDeleted(c, exist.WarrantyID)
		if !warrantyDeleted { //没有删除
			return 0, myErrors.NewErr("名单已存在")
		}
		//保卡已经删除，删除已经存在的名单
		err = g.dao.DeleteJoin(c, []int{exist.ID})
		if err != nil {
			return 0, myErrors.NewErr("旧数据处理失败").WithStack()
		}
	}

	detail, err := g.dao.GetBarcodeInfo(c, barcode)
	if err != nil {
		return 0, err
	}
	isSamePhone := 0
	if detail.ActivatedPhone == detail.CustomerPhone {
		isSamePhone = 1
	}
	promotion := model.SalesPromotionList{
		SalesPromotionID: id,
		WarrantyID:       detail.WarrantyID,
		Barcode:          detail.Barcode,
		Model:            detail.Model,
		ModelID:          detail.ModelID,
		Endpoint:         detail.Endpoint,
		BuyDate:          detail.BuyDate,
		CustomerName:     detail.CustomerName,
		CustomerPhone:    detail.CustomerPhone,
		ActivatedPhone:   detail.ActivatedPhone,
		IsSamePhone:      isSamePhone,
	}
	return g.dao.AddJoin(c, promotion)
}

// GetJoinInfo 获取参与名单详情
func (g *GormPromotionService) GetJoinInfo(c *gin.Context, id string) (map[string]any, map[string]any, error) {
	store, order, err := g.dao.GetJoinInfo(c, id)
	if err != nil {
		return nil, nil, err
	}
	activated, ok := order["activated_at"].(time.Time)
	buyDate, ok := order["buy_date"].(time.Time)
	if ok {
		order["activated_at"] = activated.Format("2006-01-02 15:04:05")
		order["buy_date"] = buyDate.Format("2006-01-02 15:04:05")
	}
	return store, order, nil
}

// GetJoinList 获取参与名单
func (g *GormPromotionService) GetJoinList(c *gin.Context, id int, page int, size int, agency string, endpoint string,
	status string, model string, barcode string) ([]action.PromotionList, int64, error) {
	// 获取参与名单
	list, total, err := g.dao.GetJoinList(c, id, page, size, agency, endpoint, status, model, barcode)
	if err != nil {
		return nil, 0, err
	}
	if len(list) == 0 {
		return nil, 0, nil
	}
	//回执图片
	ids := make([]int, 0)
	for _, v := range list {
		ids = append(ids, v.ID)
	}
	//批次查询回执图片
	receipt, err := g.dao.BatchGetReceipt(c, ids)
	if err != nil {
		return nil, 0, err
	}
	// 图片赋值处理
	for i, v := range list {
		var temp []action.PromotionListReceiptResp
		for _, r := range receipt {
			if v.ID == r.SalesPromotionListId {
				temp = append(temp, r)
			}
			sort.Sort(RepeatedCount(temp))
			list[i].Receipt = temp
		}
	}
	return list, total, err
}

func (g *GormPromotionService) GetModelTips(c *gin.Context, name string) ([]map[string]any, error) {
	return g.common.RangeModel(c, name)
}

func (g *GormPromotionService) GetEndpointTips(c *gin.Context, name string, id string) ([]map[string]any, error) {
	// 构建查询参数
	params := dao.EndpointListParams{
		Name:      name,
		TopAgency: cast.ToInt(id),
	}

	// 调用终端列表查询接口
	endpoints, _, err := g.common.GetEndpointList(c, params)
	if err != nil {
		return nil, err
	}

	// 转换为前端需要的格式
	result := make([]map[string]any, 0, len(endpoints))
	for _, endpoint := range endpoints {
		result = append(result, map[string]any{
			"id":   endpoint.ID,
			"name": endpoint.Name,
		})
	}

	return result, nil
}

func (g *GormPromotionService) GetAgencyTips(c *gin.Context, name string) ([]map[string]any, error) {
	// name模糊匹配
	var req = dao.AgencyListParams{
		Name: name,
	}

	agencies, _, err := g.common.GetAgencyList(c, req)
	if err != nil {
		return nil, err
	}
	//转换响应格式
	result := make([]map[string]any, 0, len(agencies))
	for _, agency := range agencies {
		result = append(result, map[string]any{
			"id":   agency.ID,
			"name": agency.Name,
		})
	}

	return result, nil
}

func (g *GormPromotionService) GetInfo(c *gin.Context, id string) (map[string]any, error) {
	promo, err := g.dao.GetInfo(c, id)
	if err != nil {
		return nil, err
	}
	startTime, ok := promo["start_time"].(time.Time)
	endTime, ok := promo["end_time"].(time.Time)
	receiptDay, ok := promo["receipt_day"].(time.Time)
	if ok {
		promo["start_time"] = startTime.Format("2006-01-02 15:04:05")
		promo["end_time"] = endTime.Format("2006-01-02 15:04:05")
		promo["receipt_day"] = receiptDay.Format(time.DateOnly)
	}
	modelId, ok := promo["model_id"].(string)
	if ok {
		modelIds := strings.Split(modelId, ",")
		models, _ := g.common.Models(c, modelIds)
		modelsName := make([]string, 0)
		for _, v := range models {
			modelsName = append(modelsName, v["name"].(string))
		}
		promo["model"] = models
	}
	return promo, err
}

// GetList 获取列表
func (g *GormPromotionService) GetList(c *gin.Context, param action.PromotionsReq) ([]map[string]any, int64, error) {
	list, total, err := g.dao.GetList(c, param)
	if err != nil {
		return nil, 0, err
	}
	var ids []string
	for _, v := range list {
		ids = append(ids, strings.Split(v["model"].(string), ",")...)
	}

	models, _ := g.common.Models(c, ids)

	PromotionTypeMap := consts.GetPromotionTypeMap()
	for i, v := range list {
		//时间格式 time转string
		startTime, ok := v["start_time"].(time.Time)
		endTime, ok := v["end_time"].(time.Time)
		receiptDay, ok := v["receipt_day"].(time.Time)
		if ok {
			v["start_time"] = startTime.Format("2006-01-02 15:04:05")
			v["end_time"] = endTime.Format("2006-01-02 15:04:05")
			v["receipt_day"] = receiptDay.Format(time.DateOnly)
		}
		list[i] = v
		v["type_name"] = PromotionTypeMap[cast.ToUint8(v["type"])]

		// model_name
		idStr := strings.Split(v["model"].(string), ",")

		var modelName []string
		for _, id := range idStr {
			for _, v1 := range models {
				if cast.ToString(v1["model_id"]) == id {
					modelName = append(modelName, cast.ToString(v1["name"]))
				}
			}
		}

		v["model"] = modelName
	}

	return list, total, nil
}

// Update 更新促销活动
func (g *GormPromotionService) Update(c *gin.Context, param action.PromotionUpdateReq) error {
	if g.dao.ListsStatus(c, param.ID, 0) {
		return myErrors.NewErr("活动进行中，停止更新")
	}
	if detail, _ := g.dao.GetDetail(c, int(param.ID)); detail.IsSync == 1 {
		return myErrors.NewErr("活动已同步，停止更新")
	}
	var PrizePic string
	if param.PrizePic == nil || len(param.PrizePic) == 0 {
		PrizePic = ""
	} else {
		PrizePic = param.PrizePic[0]
	}
	promo := model.SalesPromotion{
		ID:           int64(param.ID),
		Name:         param.Name,
		StartTime:    param.StartTime,
		EndTime:      param.EndTime,
		Model:        strings.Join(param.Model, ","),
		IsSamePhone:  param.IsSamePhone,
		ReceiptDay:   param.ReceiptDay,
		Prize:        param.Prize,
		HourInterval: param.HourInterval,
		PrizePic:     PrizePic,
		Rule:         param.Rule,
		Type:         param.Type,
	}
	if err := g.dao.Update(c, promo); err != nil {
		return err
	}
	return nil
}

func (g *GormPromotionService) Delete(c *gin.Context, ids []int) error {
	return g.dao.Delete(c, ids)
}

func (g *GormPromotionService) Create(c *gin.Context, parma action.PromotionAddReq) (int64, error) {
	var PrizePic string
	if parma.PrizePic == nil || len(parma.PrizePic) == 0 {
		PrizePic = ""
	} else {
		PrizePic = parma.PrizePic[0]
	}
	promo := model.SalesPromotion{
		Name:         parma.Name,
		StartTime:    parma.StartTime,
		EndTime:      parma.EndTime,
		Model:        strings.Join(parma.Model, ","),
		IsSamePhone:  parma.IsSamePhone,
		ReceiptDay:   parma.ReceiptDay,
		Prize:        parma.Prize,
		HourInterval: parma.HourInterval,
		PrizePic:     PrizePic,
		Rule:         parma.Rule,
		Type:         parma.Type,
	}
	return g.dao.Create(c, promo)
}

type RepeatedCount []action.PromotionListReceiptResp

func (r RepeatedCount) Len() int {
	return len(r)
}

func (r RepeatedCount) Less(i, j int) bool {
	return r[i].Count < r[j].Count
}

func (r RepeatedCount) Swap(i, j int) {
	r[i], r[j] = r[j], r[i]
}

func NewGormPromotionService(dao dao.PromotionDao, com dao.CommonDao) PromotionService {
	return &GormPromotionService{
		dao:    dao,
		common: com,
	}
}

// GetBarcodeInfo 根据条码获取详细信息
func (g *GormPromotionService) GetBarcodeInfo(c *gin.Context, barcode string) (*action.BarcodeResp, error) {
	return g.dao.GetBarcodeInfo(c, barcode)
}
